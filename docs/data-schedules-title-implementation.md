# Data Schedules Title/Name Feature Implementation

## Overview

This document outlines the implementation of the title/name feature for Data Schedules (Linear issue UXD-8859) to match the functionality already available in Data Alerts. The implementation leverages existing database infrastructure and follows proven patterns from the Data Alerts codebase.

## Architecture Overview

### Current Architecture (Data Schedules)
```
Frontend Forms → API Controller → Service Layer → Database
     ❌              ❌              ✅           ✅
  No title        No title      Handles all    title column
   inputs         params        parameters      exists
```

### Target Architecture (Data Schedules)
```
Frontend Forms → API Controller → Service Layer → Database
     ✅              ✅              ✅           ✅
  Title inputs    Title params   Handles all    title column
  with validation  permitted     parameters      exists
```

### Reference Architecture (Data Alerts - Already Working)
```
Frontend Forms → API Controller → Service Layer → Database
     ✅              ✅              ✅           ✅
  Title inputs    Title params   Handles all    title column
  with validation  permitted     parameters    (NOT NULL)
```

## Implementation Plan

### Phase 1: Backend API Changes
**Files to modify:**
- `app/controllers/api/v2/data_schedules_controller.rb`
- `app/controllers/email_schedules_controller.rb` (legacy API)

**Changes:**
1. Add `:title` to permitted parameters in `data_schedule_params` method
2. Ensure backward compatibility for existing schedules

### Phase 2: Serialization Layer Updates
**Files to modify:**
- `app/serializers/api/v2/data_schedule_serializer.rb`

**Changes:**
1. Add `:title` attribute to serializer
2. Ensure proper JSON response format

### Phase 3: Frontend Form Modifications
**Files to modify:**
- `app/javascript/vue_components/data_schedules/edit_modal.vue`
- `app/javascript/modules/DataSchedule/components/modals/DynamicDataSchedule.vue`
- `app/javascript/modules/DataDelivery/components/DataScheduleModal.vue`

**Changes:**
1. Add title input field with validation
2. Bind title to data model
3. Add proper form layout and styling

### Phase 4: Frontend Display Updates
**Files to modify:**
- `app/javascript/modules/DashboardAsCode/components/dashboard/preferences/DataSchedules.vue`
- `app/javascript/vue_components/email_schedule_list.vue`
- `app/javascript/vue_components/data_schedules/data_schedule_cell.vue`

**Changes:**
1. Display title in list views
2. Implement fallback to `title_or_source_title` for legacy schedules
3. Update table headers and column definitions

### Phase 5: Optional Validation Implementation
**Files to modify:**
- Frontend form components (validation rules)

**Changes:**
1. Add client-side validation (optional, unlike Data Alerts)
2. Implement proper error handling

## Code Changes Summary

### 1. Backend API Layer

#### `app/controllers/api/v2/data_schedules_controller.rb`
```ruby
# BEFORE
def data_schedule_params(attribute_name = :data_schedule)
  strong_params_for(params, attribute_name, keys: %i[source_id source_type],
                                            json_keys: %i[dest schedule dynamic_filter_presets],)
end

# AFTER
def data_schedule_params(attribute_name = :data_schedule)
  strong_params_for(params, attribute_name, keys: %i[title source_id source_type],
                                            json_keys: %i[dest schedule dynamic_filter_presets],)
end
```

#### `app/controllers/email_schedules_controller.rb`
```ruby
# BEFORE
def data_schedule_params
  _params = strong_params_for(params, :email_schedule, keys: [:id, :source_id, :source_type, :dest_type],
                                                       json_keys: [:dest, :filter_values, :schedule, :dynamic_filter_presets],)

# AFTER
def data_schedule_params
  _params = strong_params_for(params, :email_schedule, keys: [:id, :title, :source_id, :source_type, :dest_type],
                                                       json_keys: [:dest, :filter_values, :schedule, :dynamic_filter_presets],)
```

### 2. Serialization Layer

#### `app/serializers/api/v2/data_schedule_serializer.rb`
```ruby
# BEFORE
attributes :id, :source_id, :source_type

# AFTER
attributes :id, :title, :source_id, :source_type
```

### 3. Frontend Forms Layer

#### Title Input Component Pattern (from Data Alerts)
```vue
<div class="flex flex-row py-5">
  <div class="flex-col-3">
    <label class="pt-2 font-medium">Schedule title</label>
  </div>
  <div class="flex-col-9">
    <validation-field
      v-slot="{ errors, field }"
      as="div"
      name="Title"
      :model-value="mutatedDataSchedule.title"
      @update:model-value="v => mutatedDataSchedule.title = v"
    >
      <input
        v-bind="field"
        class="h-input h-form-input"
        :class="{ 'border-red-500': errors.length }"
        type="text"
        placeholder="A short description of this schedule"
      >
      <div
        v-show="errors[0]"
        class="h-form-invalid-feedback"
      >
        {{ errors[0] }}
      </div>
    </validation-field>
  </div>
</div>
```

### 4. Frontend Display Layer

#### List View Pattern (from Data Alerts)
```vue
<template #title="{ row }">
  <div class="flex flex-row justify-between">
    <div>{{ row.title || '(No title)' }}</div>
  </div>
</template>
```

## Data Alerts Pattern Reference

### 1. Form Validation Pattern
**Source:** `app/javascript/modules/DataAlerts/components/modals/DataAlertModal.vue:66-91`

Key elements:
- `validation-field` component with error handling
- Required validation rule: `rules="required"` (optional for Data Schedules)
- Model binding: `:model-value` and `@update:model-value`
- CSS classes for error states: `border-red-500`
- Placeholder text for user guidance

### 2. Parameter Processing Pattern
**Source:** `app/controllers/api/v2/data_alerts_controller.rb:183-190`

Key elements:
- Include `:title` in permitted keys array
- Consistent parameter structure across create/update operations
- Proper strong parameter filtering

### 3. Serialization Pattern
**Source:** `app/serializers/api/v2/data_alert_serializer.rb:8`

Key elements:
- Simple attribute inclusion in serializer
- Consistent JSON response structure
- No special processing needed for title field

### 4. Display Pattern
**Source:** `app/javascript/modules/DataAlerts/components/DataAlertList.vue:9-11`

Key elements:
- Template slot for title column
- Direct property access: `{{ row.title }}`
- Flexible layout with additional UI elements

## Testing Strategy

### 1. Backend API Testing
**Test files to update:**
- `spec/controllers/api/v2/data_schedules_controller_spec.rb`

**Test scenarios:**
```ruby
# Test title parameter acceptance
it 'should accept title parameter in create request' do
  post :create, params: {
    data_schedule: {
      title: 'Test Schedule Title',
      source_id: dashboard.id,
      source_type: 'Dashboard',
      # ... other params
    }
  }
  expect(response_result['data_schedule']['title']).to eq('Test Schedule Title')
end

# Test title parameter in update request
it 'should accept title parameter in update request' do
  put :update, params: {
    id: email_schedule.id,
    data_schedule: {
      title: 'Updated Schedule Title',
      # ... other params
    }
  }
  expect(response_result['data_schedule']['title']).to eq('Updated Schedule Title')
end

# Test backward compatibility
it 'should work without title parameter' do
  post :create, params: {
    data_schedule: {
      source_id: dashboard.id,
      source_type: 'Dashboard',
      # ... other params (no title)
    }
  }
  expect(response).to be_successful
  expect(response_result['data_schedule']['title']).to be_nil
end
```

### 2. Frontend Component Testing
**Test approach:**
- Unit tests for form components with title input
- Integration tests for data binding
- E2E tests for complete create/edit workflows

**Key test scenarios:**
- Title input renders correctly
- Title value is bound to component data
- Form submission includes title in payload
- Validation works as expected (if implemented)
- Legacy schedules display fallback text

### 3. Serialization Testing
**Test scenarios:**
- Title field appears in JSON response
- Null titles are handled gracefully
- Serializer maintains backward compatibility

## Legacy Compatibility

### Existing Schedules Without Titles

**Database state:**
- Existing `email_schedules` records have `title` column as `NULL`
- No data migration required

**Display strategy:**
1. **Frontend display:** Use fallback text "(No title)" when `title` is null/empty
2. **API responses:** Include `title: null` in JSON for existing schedules
3. **Model method:** Leverage existing `title_or_source_title` method for backward compatibility

**Implementation:**
```vue
<!-- In list views -->
<template #title="{ row }">
  <div>{{ row.title || '(No title)' }}</div>
</template>

<!-- Alternative using model method -->
<template #title="{ row }">
  <div>{{ row.title || row.title_or_source_title || '(No title)' }}</div>
</template>
```

### Migration Strategy

**No database migration needed** because:
1. `title` column already exists in `email_schedules` table
2. Column allows NULL values (unlike Data Alerts)
3. Existing records will continue to work without modification

**Gradual adoption:**
1. New schedules can optionally include titles
2. Existing schedules can be edited to add titles
3. No forced migration or data conversion required

### Backward Compatibility Guarantees

1. **API compatibility:** All existing API calls continue to work without title parameter
2. **Frontend compatibility:** Existing schedules display with fallback text
3. **Database compatibility:** No schema changes required
4. **Service compatibility:** Service layer already handles dynamic parameters

## Implementation Sequence

### Step 1: Backend API Changes
1. Update `data_schedule_params` in both controllers
2. Test parameter acceptance with unit tests
3. Verify backward compatibility

### Step 2: Serialization Updates
1. Add `:title` to `DataScheduleSerializer`
2. Test JSON response format
3. Verify null handling

### Step 3: Frontend Forms
1. Add title input to main edit modal
2. Add title input to dynamic schedule modal
3. Add title input to data delivery modal
4. Test form binding and submission

### Step 4: Frontend Display
1. Update dashboard preferences schedule list
2. Update general schedule list views
3. Implement fallback display logic
4. Test with existing and new schedules

### Step 5: Validation (Optional)
1. Add client-side validation if desired
2. Test validation behavior
3. Ensure graceful degradation

## Success Criteria

### Functional Requirements
- ✅ Users can add titles when creating new schedules
- ✅ Users can edit titles of existing schedules
- ✅ Titles appear in all schedule list views
- ✅ Existing schedules without titles display fallback text
- ✅ API accepts and returns title data
- ✅ Backward compatibility maintained

### Technical Requirements
- ✅ No database migrations required
- ✅ Follows Data Alerts patterns exactly
- ✅ Maintains existing API contracts
- ✅ Proper error handling and validation
- ✅ Comprehensive test coverage

### User Experience Requirements
- ✅ Intuitive title input placement in forms
- ✅ Clear placeholder text and labels
- ✅ Consistent styling with Data Alerts
- ✅ Graceful handling of legacy schedules
- ✅ Search and filtering capabilities (future enhancement)

This implementation plan ensures a smooth rollout of the title feature while maintaining full backward compatibility and following proven architectural patterns from the existing Data Alerts implementation.

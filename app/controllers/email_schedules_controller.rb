# typed: false

class EmailSchedulesController < ApplicationController
  respond_to :json, :html
  include ObjectUsageHandler

  before_action :authenticate_user!
  before_action :check_object_exceed_limit, only: [:create, :update]
  skip_before_action :verify_authenticity_token, if: -> { api_call?(:execute) }

  ALLOWED_SOURCES = %w[QueryReport Dashboard MetricSheet]
  include TypeIdSerializer

  def index
    authorize! :list, EmailSchedule

    if json_request?
      # timezone = current_user&.timezone || 'UTC'
      list = EmailSchedules::IndexQuery.new(current_tenant.id,
                                            source_type: index_params[:source_type],
                                            source_id: index_params[:source_id],
                                            dest_type: index_params[:dest_type],).execute

      list = filter_out_dest(list, EmailSchedule::SLACK_DEST) unless FeatureToggle.active?('integrations:slack',
                                                                                           current_tenant,)
      list = filter_out_dest(list, EmailSchedule::SFTP_DEST) unless FeatureToggle.active?(
        DataSource::SFTP_FEATURE_TOGGLE, current_tenant,
      )

      list.select! { |es| can? :read, es }

      render_serialized(
        list,
        EmailScheduleListSerializer,
        params: { current_user: current_user, timezone: current_browser_timezone, current_ability: current_ability },
      )
    else
      render_spa
    end
  end

  def show
    es = EmailSchedule.find(params[:id])
    authorize! :read, es
    render_serialized(
      es,
      EmailScheduleSerializer,
      params: { current_user: current_user, timezone: current_browser_timezone, current_ability: current_ability },
    )
  end

  def create
    authorize! :create, EmailSchedule
    ds_params = data_schedule_params
    authorize_source_permission(ds_params)
    data_schedule =
      DataDelivery::DelivererBuilders::DataSchedule.new(
        params: ds_params,
        schedule_params: schedule_params,
        current_user: current_user,
        current_ability: current_ability,
      ).create
    data_schedule.create_activity(
      {
        action: :create,
        parameters: ds_params.merge(schedule_params).to_unsafe_h,
      }.merge(public_activity_params),
    )

    render_json_success
  rescue StandardError => e
    error = e.to_s
    stack_trace = e.backtrace&.join("\n")
    Rails.logger.error("Failed to create new Email Schedule.\nError: #{error}.\nStacktrace: #{stack_trace}")
    render_json_error(e.message)
  end

  def update
    ds_params = data_schedule_params
    ds = EmailSchedule.find(ds_params[:id])

    authorize! :update, ds

    params = { data_schedule: ds }
    data_schedule =
      DataDelivery::DelivererBuilders::DataSchedule.new(
        params: ds_params,
        schedule_params: schedule_params,
        current_user: current_user,
        current_ability: current_ability,
      ).update(ds)
    data_schedule.create_activity(
      {
        action: :update,
        parameters: ds_params.merge(schedule_params).to_unsafe_h,
      }.merge(public_activity_params),
    )
    render_json_success
  rescue StandardError => e
    render_json_error(e.message)
  end

  def execute
    es = EmailSchedule.find(params[:id])
    authorize! :execute, es
    async_options = Notifications.email_schedule_failure_notif(es).merge(
      user_id: current_user.id,
      self_cache: true,
      worker_options: {
        queue: Job.worker_queue_for_schedules(current_tenant.id),
      },
    )
    job = es.async(async_options).execute
    render_json_dump({ status: 'ok', job_id: job.id })
  end

  def test_execute
    raise Holistics::InvalidRequest unless FeatureToggle.active?('data_schedules:test_execution', current_tenant)

    ds_params = data_schedule_params

    authorize_source_permission(ds_params)

    ds_params[:test_only] = true

    original_ds = EmailSchedule.find_by_id(params[:original_ds_id])
    authorize! :execute, original_ds if original_ds

    data_schedule =
      DataDelivery::DelivererBuilders::DataSchedule.new(
        params: ds_params,
        schedule_params: schedule_params,
        current_user: current_user,
        current_ability: current_ability,
      ).create_test(original_ds)
    job = DataSchedules::TestExecution.execute(data_schedule, current_user)

    render_json_dump({ status: 'ok', job_id: job.id })
  end

  def send_all
    source_id = send_all_params[:source_id]
    source_type = send_all_params[:source_type]

    jobs = []
    succeeded = []
    failed = []

    sidekiq_queue = Job.worker_queue_for_schedules(current_tenant.id)
    EmailSchedule.where(source_id: source_id, source_type: source_type, test_only: false).each do |es|
      if can?(:execute, es)
        async_options = {
          worker_options: {
            queue: sidekiq_queue,
          },
          user_id: current_user.id,
          self_cache: true,
        }
        jobs << es.async_defer(async_options).execute
        succeeded << es.id
      else
        failed << es.id
      end
    end

    Jobs::BatchQueueNextJobs.new.call(jobs)

    if failed.empty?
      render_json_success
    else
      render_json_error([
        "Execute email schedules (ids: #{succeeded}) succeeded.",
        "You don't have permission to send these emails (ids: #{failed})",
      ])
    end
  end

  def destroy
    es = EmailSchedule.find(params[:id])
    authorize! :destroy, es

    es.create_activity(
      :destroy,
      public_activity_params,
    )

    if es.destroy
      render_json_success
    else
      render_json_error(es)
    end
  end

  # TODO: rename to email_recipients
  def recipients
    raise CanCan::AccessDenied unless current_user.admin?

    authorize! :read, EmailSchedule
    recipients = EmailSchedule.recipients_for_email(current_tenant, number_schedules: true)
    render_json_dump(recipients)
  end

  # TODO: rename to delete_email_recipients
  def delete_recipient
    raise CanCan::AccessDenied unless current_user.admin?

    authorize! :update, EmailSchedule
    recipient = params[:recipient]
    EmailSchedule.delete_recipient(current_tenant, recipient)

    render_json_success
  end

  # take in a source_id, source_type and return the source object
  def get_source
    params.require(:source_id)
    params.require(:source_type)
    source_type = params[:source_type]
    source = authorize_source_permission(params)

    case source_type
    when 'QueryReport'
      render_serialized(source, QueryReportSerializer)
    when 'Dashboard'
      render_serialized(source, DashboardSerializer, params: { ability: current_ability })
    when 'MetricSheet'
      render_serialized(source, MetricSheetListSerializer)
    else
      # type code here
    end
  end

  def find_source(source_type, source_id)
    source_type.constantize.find(source_id)
  end

  def parse_dynamic_string
    str = params.require(:str)
    data_schedule = EmailSchedule.new(dynamic_string_params)
    if data_schedule.source_type == 'Dashboard' && data_schedule.dest_type == 'SftpDest'
      sftp_dest = params.dig(:email_schedule, :dest)&.permit(:dashboard_widget_id)
      data_schedule.dest = SftpDest.new(sftp_dest) if sftp_dest.present?
    end
    authorize_source_permission(dynamic_string_params)
    parsed_string = data_schedule.parse_dynamic_string(str)
    render_json_dump({ parsed_string: parsed_string })
  end

  private

  def public_activity_params
    {
      owner: current_user,
      tenant_id: current_user.tenant_id,
      impersonator_id: impersonator_id,
      ip: client_ip,
    }
  end

  def index_params
    unless params[:source_type].nil? || ALLOWED_SOURCES.include?(params[:source_type])
      raise Holistics::InvalidParameter,
            "Invalid source type: '#{params[:source_type]}'"
    end
    unless params[:source_id].nil? || params[:source_id].to_i != 0
      raise Holistics::InvalidParameter,
            "Invalid source id: '#{params[:source_id]}'"
    end
    unless params[:dest_type].nil? || EmailSchedule.allow_dest?(params[:dest_type])
      raise Holistics::InvalidParameter,
            "Invalid dest type: '#{params[:dest_type]}'"
    end

    params.permit(:source_type, :source_id, :dest_type)
  end

  def schedule_params
    schedule_params_for(:email_schedule)
  end

  def data_schedule_params
    _params = strong_params_for(params, :email_schedule, keys: [:id, :title, :source_id, :source_type, :dest_type],
                                                         json_keys: [:dest, :filter_values, :schedule, :dynamic_filter_presets],)
    # Base url for url in email content
    if _params[:dest_type] == 'EmailDest'
      _params[:dest][:options] ||= {}
      _params[:dest][:options][:base_url] = request.base_url
    end
    _params
  end

  def dynamic_string_params
    @dynamic_string_params ||= strong_params_for(params, :email_schedule, keys: [:source_id, :source_type, :dest_type])
  end

  def send_all_params
    params.permit(:source_id, :source_type)
  end

  def filter_out_dest(list, filtered_dest_type)
    list.reject { |ds| ds.dest_type == filtered_dest_type }
  end

  def authorize_source_permission(params)
    source_type = params[:source_type]
    source_id = params[:source_id]

    raise Holistics::InvalidRequest, "Source #{source_type} not allowed" unless source_type.in?(ALLOWED_SOURCES)

    source = typeid_to_object(source_type, source_id)
    authorize! :read, source

    source
  end
end

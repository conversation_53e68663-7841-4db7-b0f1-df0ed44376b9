# typed: true
# frozen_string_literal: true

module Ai
  class TenantSettings < T::Struct
    class AgentSettings < T::Struct
      const :enabled, T::<PERSON><PERSON><PERSON>, default: true
    end

    const :agents, T::Hash[Types::AgentName, AgentSettings]

    class << self
      extend T::Sig

      def coerce_from(hash)
        settings = (hash || {}).slice(:agents)

        defaults = Ai::Agents::Factory.all.to_h do |agent|
          [agent.agent_name.serialize, agent.default_settings]
        end

        # need to stringify_keys here because Sorbet AgentName enum is of type string
        settings[:agents] = defaults.merge(settings.fetch(:agents, {})&.try(:stringify_keys))
                                    .slice(*Ai::Agents::Factory.all.map(&:agent_name).map(&:serialize))
        super(settings)
      end
    end
  end
end

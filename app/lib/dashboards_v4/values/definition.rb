# typed: true

# TODO: update this struct with the newest dashboard as code syntax
module DashboardsV4::Values
  class Definition < T::Struct
    class Type < T::Enum
      enums do
        Filter = new('FilterBlock')
        Pop = new('PopBlock')
        DateDrill = new('DateDrillBlock')
        Text = new('TextBlock')
        Viz = new('VizBlock')
        Error = new('ErrorBlock')
      end
    end

    class FilterSourceType < T::Enum
      enums do
        Field = new('DmFieldFilterSource')
        Manual = new('ManualFilterSource')
      end
    end

    class DashboardViewType < T::Enum
      enums do
        Canvas = new('CanvasLayout')
        Linear = new('LinearLayout')
        Tab = new('TabLayout')
      end
    end

    class DashboardInteractionType < T::Enum
      enums do
        Filter = new('FilterInteraction')
        Pop = new('PopInteraction')
        DateDrill = new('DateDrillInteraction')
        CrossFilter = new('CrossFilterInteraction')
        LinkedFilter = new('LinkedFilterInteraction')
        Error = new('ErrorInteraction')
      end
    end

    class DmFieldFilterSource < T::Struct
      const :source_type, FilterSourceType::Field, default: FilterSourceType::Field
      const :data_set_id, T.any(Integer, String)
      const :field_path, FieldPath

      lazy
      def full_field_path
        DataModeling::Values::FieldPath.new(field_name: field_path.field_name, model_id: field_path.model_id,
                                            data_set_id: data_set_id,)
      end

      sig { returns(T::Boolean) }
      def dm_field_filter_source?
        true
      end
    end

    class ManualFilterSource < T::Struct
      const :source_type, FilterSourceType::Manual, default: FilterSourceType::Manual
      const :manual_options, T.untyped

      sig { returns(T::Boolean) }
      def dm_field_filter_source?
        false
      end
    end

    class VizTheme < T::Struct
      class FontWeight < T::Enum
        enums do
          Light = new('light')
          Normal = new('normal')
          Medium = new('medium')
          Semibold = new('semibold')
          Bold = new('bold')
          Extrabold = new('extrabold')
        end
      end

      class TableTheme < T::Struct
        class TableGeneralTheme < T::Struct
          const :bg_color, T.nilable(String)
          const :font_size, T.nilable(String)
          const :font_color, T.nilable(String)
          const :font_family, T.nilable(String)
          const :font_weight, T.nilable(FontWeight)
          const :hover_color, T.nilable(String)
          const :banding_color, T.nilable(String)
        end

        class TableHeaderTheme < T::Struct
          const :bg_color, T.nilable(String)
          const :font_size, T.nilable(String)
          const :font_color, T.nilable(String)
          const :font_family, T.nilable(String)
          const :font_weight, T.nilable(FontWeight)
        end

        class TableSubTitleTheme < T::Struct
          const :font_size, T.nilable(String)
          const :font_weight, T.nilable(FontWeight)
          const :font_color, T.nilable(String)
        end

        class TableBorderTheme < T::Struct
          const :border_color, T.nilable(String)
          const :border_width, T.nilable(String)
          const :grid_color, T.nilable(String)
        end

        const :_id, T.nilable(String)
        const :general, T.nilable(TableGeneralTheme)
        const :header, T.nilable(TableHeaderTheme)
        const :row_header, T.nilable(TableHeaderTheme)
        const :column_header, T.nilable(TableHeaderTheme)
        const :sub_title, T.nilable(TableSubTitleTheme)
        const :border, T.nilable(TableBorderTheme)
      end

      const :_id, T.nilable(String)
      const :table, T.nilable(TableTheme)
    end

    class Viz < T::Struct
      const :dataset_id, T.any(String, Integer)
      const :viz_setting, VizSetting
      const :theme, T.nilable(VizTheme)

      sig { returns(T.any(String, Integer)) }
      def data_set_id
        dataset_id
      end
    end

    class PopBlockDef < T::Struct
      include Block

      class Settings < T::Struct
        const :hide_label, T::Boolean, default: false
        const :hide_controls, T::Boolean, default: false
      end

      const :type, Type::Pop
      const :uname, String
      const :label, T.nilable(String)
      const :description, T.nilable(String)
      const :pop, DashboardsV4::Values::ControlBlockDefinition
      const :settings, Settings
      const :theme, T.untyped

      alias definition pop
    end

    class DateDrillBlockDef < T::Struct
      include Block

      class Settings < T::Struct
        const :hide_label, T::Boolean, default: false
        const :hide_controls, T::Boolean, default: false
      end

      const :type, Type::DateDrill
      const :uname, String
      const :label, T.nilable(String)
      const :description, T.nilable(String)
      const :date_drill, DashboardsV4::Values::ControlBlockDefinition
      const :settings, Settings
      const :theme, T.untyped

      alias definition date_drill
    end

    class VizBlockDef < T::Struct
      include Block

      class Settings < T::Struct
        const :hide_label, T::Boolean, default: false
        const :hide_controls, T::Boolean, default: false
      end

      const :type, Type::Viz
      const :label, T.nilable(String)
      const :uname, String
      const :viz, Viz
      const :description, T.nilable(String)
      const :settings, Settings
      const :theme, T.untyped

      alias source viz

      lazy; sig { returns(String) }
      def display_title
        label || uname
      end

      alias title display_title

      sig { override.returns(T.any(String, Integer)) }
      def dataset_id
        viz.dataset_id
      end
    end

    class TextBlockDef < T::Struct
      include Block

      class Settings < T::Struct
        const :hide_controls, T::Boolean, default: false
      end

      const :type, Type::Text
      const :uname, String
      const :content, String
      const :settings, Settings
      const :theme, T.untyped
    end

    class BlockPosition < T::Struct
      class Position < T::Struct
        const :x, Integer
        const :y, Integer
        const :w, Integer
        const :h, Integer
      end

      const :position, Position
      const :layer, Integer
    end

    class CanvasLayout < T::Struct
      const :type, DashboardViewType::Canvas
      const :uname, String
      const :label, T.nilable(String)
      const :width, T.nilable(Integer)
      const :height, T.nilable(Integer)
      const :grid_size, T.nilable(Integer)
      const :default_zoom, T.nilable(T.any(Integer, Float, String))
      const :blocks, T::Hash[T.untyped, BlockPosition]
      const :theme, T.untyped
      const :mobile, T.untyped
    end

    class LinearLayout < T::Struct
      const :type, DashboardViewType::Linear
      const :uname, String
      const :label, T.nilable(String)

      const :blocks, T.nilable(T::Array[String])
    end

    class TabLayout < T::Struct
      const :type, DashboardViewType::Tab
      const :uname, String
      const :label, T.nilable(String)
      const :tabs, T::Array[T.any(CanvasLayout, LinearLayout)]
    end

    class FilterInteraction < T::Struct
      include IcVizMapping

      const :type, DashboardInteractionType::Filter
      const :from, String
      const :to, String
      const :field_path, T.nilable(FieldPath)
      const :disabled, T.nilable(T::Boolean)
      const :aggregation, T.nilable(String)

      sig { params(other: FilterInteraction).returns(T::Boolean) }
      def equal_with(other)
        from == other.from && to == other.to &&
          !!disabled == !!other.disabled &&
          aggregation == other.aggregation &&
          field_path.to_h.compact.slice(:model_id, :field_name) ==
            other.field_path.to_h.compact.slice(:model_id, :field_name)
      end
    end

    class PopInteraction < T::Struct
      include IcVizMapping

      const :type, DashboardInteractionType::Pop
      const :from, String
      const :to, String
      const :field_path, FieldPath
      const :disabled, T.nilable(T::Boolean)
    end

    class DateDrillInteraction < T::Struct
      include IcVizMapping

      const :type, DashboardInteractionType::DateDrill
      const :from, String
      const :to, String
      const :field_path, FieldPath
      const :disabled, T.nilable(T::Boolean)
    end

    class CrossFilterInteraction < T::Struct
      const :type, DashboardInteractionType::CrossFilter
      const :from, String
      const :to, String
      const :disabled, T.nilable(T::Boolean)
    end

    class LinkedFilterInteraction < T::Struct
      const :type, DashboardInteractionType::LinkedFilter
      const :from, String
      const :to, String
      const :field_path, T.nilable(FieldPath)
      const :disabled, T.nilable(T::Boolean)
    end

    class DashboardSettings < T::Struct
      const :timezone, T.nilable(String)
      const :autorun, T.nilable(T::Boolean)
      const :allow_timezone_change, T.nilable(T::Boolean)
      const :cache_duration, T.nilable(Integer)
    end

    ControlBlockDef = T.type_alias { T.any(FilterBlockDef, PopBlockDef, DateDrillBlockDef) }

    InteractionDef = T.type_alias do
      T.any(
        FilterInteraction,
        PopInteraction,
        DateDrillInteraction,
        CrossFilterInteraction,
        LinkedFilterInteraction,
      )
    end

    const :uname, String
    const :title, T.nilable(String)
    const :description, T.nilable(String), default: nil
    # const :owner, String
    const :blocks, T::Array[Block]
    const :interactions, T::Array[InteractionDef]
    const :views, T::Array[T.any(LinearLayout, CanvasLayout, TabLayout)]
    const :settings, T.nilable(DashboardSettings)
    const :theme, T.untyped

    lazy; sig { returns(T::Array[T.any(FilterBlockDef, PopBlockDef, DateDrillBlockDef)]) }
    def controls
      blocks = self.blocks
      return [] if blocks.nil?

      T.cast(
        blocks.select do |block|
          block.is_a?(FilterBlockDef) || block.is_a?(PopBlockDef) || block.is_a?(DateDrillBlockDef)
        end,
        T::Array[T.any(FilterBlockDef, PopBlockDef, DateDrillBlockDef)],
      )
    end

    lazy; sig { returns(T::Hash[String, Block]) }
    def block_map
      blocks = self.blocks
      return {} if blocks.nil?

      blocks.to_h { |b| [b.uname, b] }
    end

    lazy; sig { returns(T::Array[FilterBlockDef]) }
    def filter_blocks
      T.cast(
        controls.select { |block| block.is_a?(FilterBlockDef) },
        T::Array[FilterBlockDef],
      )
    end

    lazy; sig { returns(T::Array[VizBlockDef]) }
    def viz_blocks
      T.cast(
        blocks.select { |block| block.is_a?(VizBlockDef) },
        T::Array[VizBlockDef],
      )
    end

    lazy; sig { returns(T::Hash[String, T.any(FilterBlockDef, PopBlockDef, DateDrillBlockDef)]) }
    def control_map
      controls.to_h { |f| [f.uname, f] }
    end

    # [from_block, to_block] => filter_interaction
    lazy; sig { returns(T::Hash[String, T::Hash[String, FilterInteraction]]) }
    def filter_interactions_map
      res = {}

      interactions.each do |interaction|
        next unless interaction.is_a?(FilterInteraction)

        res[interaction.from] ||= {}
        res[interaction.from][interaction.to] = interaction
      end

      res
    end

    sig { params(from: String, to: String).returns(T.nilable(FilterInteraction)) }
    def find_filter_interaction(from, to)
      filter_interactions_map.dig(from, to)
    end

    sig do
      params(control_ids: T::Array[String]).returns(T::Array[T.any(FilterBlockDef, PopBlockDef, DateDrillBlockDef)])
    end
    def find_controls(control_ids)
      control_ids.uniq.map { |control_id| control_map[control_id] }.compact
    end

    sig { params(uname: String).returns(T.nilable(Block)) }
    def find_block(uname)
      block_map[uname]
    end

    sig { returns(T::Array[T.any(Integer, String)]) }
    def included_data_set_ids
      blocks.map(&:dataset_id).compact.uniq
    end

    class << self
      extend T::Sig
      extend HOtel::Traceable

      sig do
        params(
          aml_json: Hash,
          data_sources: T::Hash[String, Modeling::Values::DataSource],
          options: T.nilable(HolisticsAml::MapperOptions),
        ).returns(T.attached_class)
      end
      def from_json(aml_json, data_sources: {}, options: nil)
        coerce_from(aml_json)
      end

      sig { params(hash: Hash).returns(T.attached_class) }
      def coerce_from(hash)
        hash = hash.deep_symbolize_keys
        hash = hash.merge(
          blocks: Utils.assert_type(hash[:blocks], Array, 'blocks').map { |bl| build_block(bl) },
          views: Utils.assert_type(hash[:views], Array, 'views').map { |view| build_view(view) },
          settings: hash[:settings]&.slice(:timezone, :autorun, :allow_timezone_change, :cache_duration),
          interactions: Utils.assert_type(hash[:interactions], Array, 'interactions').map do |interaction|
            build_interaction(interaction)
          end.compact,
        )
        super(hash)
      rescue Holistics::InvalidOperation => e
        uname = hash[:uname]
        raise Holistics::InvalidOperation, "Dashboard \"#{uname}\": #{e.message}"
      end
      otel_wrap(:coerce_from)

      def build_view(hash)
        case hash[:type]
        when DashboardViewType::Canvas.serialize
          CanvasLayout.new(
            hash.merge(
              type: DashboardViewType::Canvas,
              blocks: build_canvas_layout_blocks(Utils.assert_type(hash[:blocks], Hash, 'blocks')),
            ),
          )
        when DashboardViewType::Linear.serialize
          LinearLayout.new(hash.merge(type: DashboardViewType::Linear))
        when DashboardViewType::Tab.serialize
          tabs = hash[:tabs].map do |tab|
            raise Holistics::InvalidOperation, "Invalid tab view #{tab[:type]}" unless [DashboardViewType::Canvas.serialize, DashboardViewType::Linear.serialize].include?(tab[:type])
            build_view(tab)
          end
          TabLayout.new(
            hash.merge(
              type: DashboardViewType::Tab,
              tabs: tabs,
            ),
          )
        else
          raise Holistics::InvalidOperation, "Invalid view #{hash[:type]}"
        end
      end

      def build_canvas_layout_blocks(hash)
        hash.each do |key, value|
          position = value[:position]
          hash[key] = BlockPosition.new(
            value.merge(position: BlockPosition::Position.new(position.slice(:x, :y, :w, :h))),
          )
        end
      end

      def build_interaction(hash)
        case hash[:type]
        when DashboardInteractionType::Filter.serialize
          FilterInteraction.new(
            hash.merge(
              type: DashboardInteractionType::Filter,
              field_path: if hash[:field_path]
                            FieldPath.new(hash[:field_path].slice(:model_id, :data_set_id, :field_name))
                          else
                            nil
                          end,
            ).slice(:type, :from, :to, :field_path, :disabled, :aggregation),
          )
        when DashboardInteractionType::LinkedFilter.serialize
          LinkedFilterInteraction.new(
            hash.merge(
              type: DashboardInteractionType::LinkedFilter,
              field_path: if hash[:field_path]
                            FieldPath.new(hash[:field_path].slice(:model_id, :data_set_id,
                                                                  :field_name,))
                          else
                            nil
                          end,
            ).slice(:type, :from, :to, :field_path, :disabled),
          )
        when DashboardInteractionType::Pop.serialize
          PopInteraction.new(
            hash.merge(
              type: DashboardInteractionType::Pop,
              field_path: FieldPath.new(hash[:field_path].slice(:model_id, :data_set_id,
                                                                :field_name,)),
            ).slice(:type, :from, :to, :field_path, :disabled),
          )
        when DashboardInteractionType::DateDrill.serialize
          DateDrillInteraction.new(
            hash.merge(
              type: DashboardInteractionType::DateDrill,
              field_path: FieldPath.new(hash[:field_path].slice(:model_id, :data_set_id,
                                                                :field_name,)),
            ).slice(:type, :from, :to, :field_path, :disabled),
          )
        when DashboardInteractionType::CrossFilter.serialize
          CrossFilterInteraction.new(
            hash.merge(
              type: DashboardInteractionType::CrossFilter
            ).slice(:type, :from, :to, :disabled),
          )
        when DashboardInteractionType::Error.serialize
          # ErrorInteraction: ignore
          nil
        else
          raise Holistics::InvalidOperation, "Invalid interaction #{hash[:type]}"
        end
      end

      def build_filter_condition(hash)
        return nil unless hash.present?

        DataModeling::Values::Condition.new(
          hash.merge(
            options:
              if hash[:options]
                DataModeling::Values::ConditionOptions::Factory.new.call(hash[:operator], hash[:options])
              else
                nil
              end,
          ),
        )
      end

      def build_block(hash)
        case hash[:type]
        when Type::Filter.serialize
          source = hash[:filter][:filter_source] || {}
          source =
            if source[:source_type] == FilterSourceType::Field.serialize
              field_path = source[:field_path].merge(data_set_id: source[:data_set_id])
              DmFieldFilterSource.new(source.slice(:data_set_id).merge(field_path: FieldPath.new(field_path)))
            else
              ManualFilterSource.new(source.slice(:manual_options))
            end
          filter = hash[:filter]
          FilterBlockDef.new(
            hash.merge(
              type: Type::Filter,
              filter: ControlBlockDefinition.new(
                hash[:filter].slice(:input_type, :filter_type, :is_shareable, :label).merge(
                  filter_source: source,
                  default_condition: build_filter_condition(filter[:default_condition]),
                ),
              ),
              settings: FilterBlockDef::Settings.new(
                hide_controls: hash.dig(:settings, :hide_controls).is_true?,
                hide_label: hash.dig(:settings, :hide_label).is_true?,
                enable_drillthrough: hash.dig(:settings, :enable_drillthrough).is_true?,
                drillthrough: FilterBlockDef.build_drillthrough(hash.dig(:settings, :drillthrough)),
              ),
            ),
          )
        when Type::Pop.serialize
          source = hash[:pop][:filter_source] || {}
          source = ManualFilterSource.new(source.slice(:manual_options))
          pop = hash[:pop]
          PopBlockDef.new(
            hash.merge(
              type: Type::Pop,
              pop: ControlBlockDefinition.new(
                hash[:pop].slice(:input_type, :filter_type, :is_shareable, :label).merge(
                  filter_source: source,
                  default_condition: build_filter_condition(pop[:default_condition]),
                ),
              ),
              settings: PopBlockDef::Settings.new(
                hide_controls: hash.dig(:settings, :hide_controls).is_true?,
                hide_label: hash.dig(:settings, :hide_label).is_true?,
              ),
            ),
          )
        when Type::DateDrill.serialize
          source = hash[:date_drill][:filter_source] || {}
          source = ManualFilterSource.new(source.slice(:manual_options))
          date_drill = hash[:date_drill]
          DateDrillBlockDef.new(
            hash.merge(
              type: Type::DateDrill,
              date_drill: ControlBlockDefinition.new(
                hash[:date_drill].slice(:input_type, :filter_type, :is_shareable, :label).merge(
                  filter_source: source,
                  default_condition: build_filter_condition(date_drill[:default_condition]),
                ),
              ),
              settings: DateDrillBlockDef::Settings.new(
                hide_controls: hash.dig(:settings, :hide_controls).is_true?,
                hide_label: hash.dig(:settings, :hide_label).is_true?,
              ),
            ),
          )
        when Type::Text.serialize
          TextBlockDef.new(
            hash.merge(
              type: Type::Text,
              settings: TextBlockDef::Settings.new(
                hide_controls: hash.dig(:settings, :hide_controls).is_true?,
              ),
            ),
          )
        when Type::Viz.serialize
          viz = hash[:viz]
          VizBlockDef.new(
            hash.merge(
              type: Type::Viz,
              viz: Viz.new(
                dataset_id: viz[:dataset_id],
                viz_setting: VizSetting.new(viz[:viz_setting]),
                theme: viz[:theme] ? VizTheme.from_hash(viz[:theme]) : nil,
              ),
              settings: VizBlockDef::Settings.new(
                hide_controls: hash.dig(:settings, :hide_controls).is_true?,
                hide_label: hash.dig(:settings, :hide_label).is_true?,
              ),
            ),
          )
        when Type::Error.serialize
          # ErrorBlock: show proper error message
          raise Holistics::InvalidOperation, "Block \"#{hash[:uname]}\": #{hash[:message]}"
        else
          raise Holistics::InvalidOperation, "Invalid block type: #{hash[:type]}"
        end
      end
    end
  end
end

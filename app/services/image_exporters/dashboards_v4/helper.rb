# frozen_string_literal: true
# typed: true

module ImageExporters::DashboardsV4
  class Helper < T::Struct

    # @param widget_viz_conditions_map [DynamicFilters::Values::WidgetVizConditionsMap]
    # @return [DashboardDefinition]
    sig { params(dashboard: Dashboard, widget_viz_conditions_map: DynamicFilters::Values::WidgetVizConditionsMap).returns(T.untyped) }
    def self.dashboard_definition_with_filter(dashboard, widget_viz_conditions_map)
      # Convert widget viz conditions to hash viz conditions
      conditions = widget_viz_conditions_map.values.flatten.each_with_object({}) do |condition, hash|
        hash[condition[:dynamic_filter_id]] = condition[:condition].to_h
      end

      # Add viz conditions to default condition to show the value in filter
      dashboard_definition_with_filter = dashboard.definition.deep_dup
      dashboard_definition_with_filter['blocks'] = dashboard_definition_with_filter['blocks'].map do |block|
        block_id = ::DashboardsV4::Services::MaterializedBlockOperations.generate_block_id(dashboard, block['uname'])
        next block unless conditions[block_id]

        case block['type']
        when ::DashboardsV4::Values::Definition::Type::Filter.serialize
          ::Utils.set_hash(block, ['filter', 'default_condition'], conditions[block_id])
        when ::DashboardsV4::Values::Definition::Type::DateDrill.serialize
          ::Utils.set_hash(block, ['date_drill', 'default_condition'], conditions[block_id])
        else
          ::Utils.set_hash(block, ['pop', 'default_condition'], conditions[block_id])
        end
        block
      end
      dashboard_definition_with_filter
    end

    sig { params(view: T.nilable(T.any(DashboardsV4::Values::Definition::CanvasLayout, DashboardsV4::Values::Definition::LinearLayout))).returns([Integer, Integer]) }
    def self.extract_page_width_and_height(view)
      first_view = view
      first_view = {} if view.is_a?(::DashboardsV4::Values::Definition::LinearLayout) || !first_view

      # Note: 20px = 20px(padding of dashboard)
      #       100px = 20px(padding of dashboard) + 80px header
      page_width = (first_view['width'] || 1200) + 20
      page_height = (first_view['height'] || 800) + 100
      [page_width, page_height]
    end

    sig do
      params(
        dashboard: Dashboard,
        view: T.nilable(T.any(DashboardsV4::Values::Definition::CanvasLayout, DashboardsV4::Values::Definition::LinearLayout)),
      ).returns(T::Hash[Symbol, Integer])
    end
    def self.extract_page_theme_border_width(dashboard, view)
      # Use dig to safely navigate through nested hashes with default empty hash
      dashboard_border_width = dashboard.definition_struct&.theme&.dig(:canvas, :border, :border_width)&.to_h&.deep_symbolize_keys || {}

      # Safely access view theme border width
      view_border_width = view.is_a?(::DashboardsV4::Values::Definition::CanvasLayout) ? (view.theme&.dig(:border, :border_width)&.to_h&.deep_symbolize_keys || {}) : {}

      # Merge border widths with view taking precedence
      border_width = dashboard_border_width.merge(view_border_width)

      # Extract values with default 0, using string keys since JSON keys are strings
      # Convert to integer and handle nil values
      {
        border_top_width: (border_width[:top] || 0).to_i,
        border_left_width: (border_width[:left] || 0).to_i,
        border_right_width: (border_width[:right] || 0).to_i,
        border_bottom_width: (border_width[:bottom] || 0).to_i,
      }
    end

    sig { params(dashboard: Dashboard, view: T.nilable(T.any(DashboardsV4::Values::Definition::CanvasLayout, DashboardsV4::Values::Definition::LinearLayout))).returns([Integer, Integer]) }
    def self.extract_export_page_width_and_height(dashboard, view)
      page_width, page_height = extract_page_width_and_height(view)
      page_theme_border_width = extract_page_theme_border_width(dashboard, view)

      page_width += page_theme_border_width[:border_left_width] || 0
      page_width += page_theme_border_width[:border_right_width] || 0
      page_height += page_theme_border_width[:border_top_width] || 0
      page_height += page_theme_border_width[:border_bottom_width] || 0

      [page_width, page_height]
    end
  end
end

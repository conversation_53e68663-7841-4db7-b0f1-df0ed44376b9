# typed: true

module ImageExporters::DashboardsV4
  class Tab
    extend T::Sig
    RENDERER_PACK = 'pptr_dashboard'.freeze

    sig do
      params(
        dashboard: Dashboard,
        job: Job,
        permission_rules: T.untyped,
        query_processing_timezone: T.untyped,
        widgets: T.nilable(T::Array[Dashboards::Types::TypedWidget]),
        widget_viz_conditions_map: T.untyped,
        check_widget_permission: T::Boolean,
        tab_unames: T::Array[String],
      ).void
    end
    def initialize(
      dashboard,
      job:,
      permission_rules:,
      query_processing_timezone:,
      widgets: nil,
      widget_viz_conditions_map: {},
      check_widget_permission: true,
      tab_unames: []
    )
      @dashboard = dashboard
      @widget_viz_conditions_map = widget_viz_conditions_map || {}
      @query_processing_timezone = query_processing_timezone
      @job = job
      @permission_rules = permission_rules
      @check_widget_permission = check_widget_permission
      @tab_unames = tab_unames
      # <PERSON><PERSON> selected widgets only or all widgets if not specified
      @widgets = widgets || @dashboard.tab_layout&.blocks(@tab_unames)

      if check_widget_permission
        @widget_ability = DashboardWidgetsAbility.new(job.user)
        Abilities::Services::AllowPublicDrillthroughService.new.call(@widget_ability)
      end
    end

    # @param widget_cache_map: can be either
    #   * [Hash] map between widget ids and their cache key. Used to generate data for each widget from cache if `results_data` is not provided
    #   * [Dashboards::Values::ReportWidgetCacheMap]
    # @param format [T.any(String, ImageExporters::Format)]: output format
    # @param results_data [Array]: widgets' results data that will be passed into the renderer packs. Use this to export same data multiple times without having to generate the data again
    sig { params(widget_cache_map: T.untyped, format: T.any(String, ImageExporters::Format), results_data: T.untyped).returns(ImageExporters::DashboardsV4::Result) }
    def export(widget_cache_map, format: 'png', results_data: nil)
      format_enum = ImageExporters::Format.from(format)
      results = results_data || generate_results_data(widget_cache_map)

      if single_tab_export?
        export_single_tab(results, format_enum)
      elsif format_enum == ImageExporters::Format::PDF
        export_multiple_tabs_as_pdf(results)
      else
        export_multiple_png_tabs_as_zip(results)
      end
    end

    sig { params(widget_cache_map: T.untyped).returns(T.untyped) }
    def generate_results_data(widget_cache_map)
      ImageExporters::Services::WidgetResultGenerator.new(
        widgets: @widgets,
        job: @job,
        permission_rules: @permission_rules,
        query_processing_timezone: @query_processing_timezone,
        widget_viz_conditions_map: @widget_viz_conditions_map,
        check_widget_permission: @check_widget_permission,
        should_build_table_options: !@dashboard.is_v4? && !@dashboard.is_v3?,
      ).generate(widget_cache_map)
    end

    private
    sig { returns(DashboardsV4::Materialized::View::TabLayout) }
    def tab_layout
      # NOTE: This is safe because we check for tab_layout presence in the constructor
      T.must(@dashboard.tab_layout)
    end

    def export_single_layout(format, page_width, page_height, renderer_input)
      ImageExporters::PuppeteerRunner.new(renderer_pack: RENDERER_PACK, job: @job).execute(
        format: format.to_s,
        width: page_width,
        height: page_height,
        renderer_input: renderer_input,
        timeout: [@widgets.size * 3, 40].max,
      ) do |outfile, log|
        if format == ImageExporters::Format::PDF
          @job.logger.info 'Post processing pdf'
          page_crop_map = ImageExporters::Helper.get_page_crop_map_from_log(log, page_width: page_width,
                                                                                 page_height: page_height,)
          file_content = Pdf::Crop.new(outfile.path, job: @job).execute(page_crop_map: page_crop_map)
          file_content
        else
          File.read(outfile.path)
        end
      end
    end

    sig { returns(T::Boolean) }
    def single_tab_export?
      tab_layout.get_tabs(@tab_unames).length == 1
    end

    sig { params(results: T.untyped, format: ImageExporters::Format).returns(ImageExporters::DashboardsV4::SingleFileResult) }
    def export_single_tab(results, format)
      # This is safe because we check length previously
      tab = T.must(tab_layout.get_tabs(@tab_unames).first)
      @job.logger.debug("Exporting #{format} file for tab #{tab.uname}: #{tab.label || ''}")

      renderer_input, page_width, page_height = generate_renderer_tab_input_v4(results, tab)
      file_content = export_single_layout(format, page_width, page_height, renderer_input)
      @job.logger.debug("Exported #{format} file for tab #{tab.uname}: #{tab.label || ''}")

      create_single_file_result(file_content, format, "#{normalize_file_name(tab.label || tab.uname)}.#{format.extension}")
    end

    sig { params(results: T.untyped).returns(ImageExporters::DashboardsV4::SingleFileResult) }
    def export_multiple_tabs_as_pdf(results)
      tab_contents = collect_tab_contents(results, ImageExporters::Format::PDF)
      pdf_contents = tab_contents.map { |_, file_content| file_content }

      pdf_result = Pdf::Merge.new(job: @job).execute(pdf_contents)
      create_single_file_result(pdf_result, ImageExporters::Format::PDF, "#{normalize_file_name(@dashboard.title || @dashboard.id.to_s)}.#{ImageExporters::Format::PDF.extension}")
    end


    sig do
      params(results: T.untyped, format: ImageExporters::Format).returns(
        T::Array[
          [
            T.any(DashboardsV4::Values::Definition::CanvasLayout, DashboardsV4::Values::Definition::LinearLayout),
            T.untyped,
          ],
        ],
      )
    end
    def collect_tab_contents(results, format)
      tab_layout.get_tabs(@tab_unames).map do |tab|
        @job.logger.debug("Exporting #{format} file for tab #{tab.uname}: #{tab.label || ''}")
        renderer_input, page_width, page_height = generate_renderer_tab_input_v4(results, tab)
        file_content = export_single_layout(format, page_width, page_height, renderer_input)
        @job.logger.debug("Exported #{format} file for tab #{tab.uname}: #{tab.label || ''}")
        [tab, file_content]
      end
    end

    sig { params(results: T.untyped).returns(ImageExporters::DashboardsV4::SingleFileResult) }
    def export_multiple_png_tabs_as_zip(results)
      tab_contents = collect_tab_contents(results, ImageExporters::Format::PNG)
      zip_file = Tempfile.new('dashboard_export.zip')
      @job.logger.debug 'Start zipping files'
      Zip::File.open(zip_file.path, Zip::File::CREATE) do |zip|
        tab_contents.each_with_index do |(tab, file_content), index|
          # NOTE: Add index prefix to ensure proper ordering and avoid collisions
          filename = "#{normalize_file_name(format('%<index>02d_%<label>s_%<uname>s', index: index + 1, label: tab.label, uname: tab.uname))}.#{ImageExporters::Format::PNG.extension}"
          zip.get_output_stream(filename) do |stream|
            stream.write(file_content)
          end
        end
      end
      @job.logger.debug 'End zipping files'

      # Read the zip file content
      zip_content = File.binread(zip_file.path.to_s)

      # Clean up the zip temp file
      zip_file.close
      zip_file.unlink
      create_single_file_result(zip_content, ImageExporters::Format::ZIP, "#{normalize_file_name(@dashboard.title || @dashboard.id.to_s)}.#{ImageExporters::Format::ZIP.extension}")
    end

    sig { params(content: T.untyped, format: ImageExporters::Format, filename: T.nilable(String)).returns(ImageExporters::DashboardsV4::SingleFileResult) }
    def create_single_file_result(content, format, filename)
      ImageExporters::DashboardsV4::SingleFileResult.new(
        content: content,
        format: format,
        filename: filename,
      )
    end

    sig { params(results: T.untyped, tab: T.untyped).returns(T.untyped) }
    def generate_renderer_tab_input_v4(results, tab)
      unames_blocks = tab_layout.uname_blocks_by_tabs(@tab_unames)

      dashboard_ability = DashboardsAbility.new(@job.user)
      renderer_input = {
        dashboard: {
          id: @dashboard.id,
          title: @dashboard.title || @dashboard.uname,
          version: @dashboard.version,
          definition: dashboard_definition_with_filter,
          definition_aml: '',
          permissions: {
            can_crud: dashboard_ability.can?(:crud, @dashboard),
            can_read: dashboard_ability.can?(:read, @dashboard),
          },
          settings: {
            timezone: @query_processing_timezone,
            tab_uname: tab.uname,
            tab_label: tab.label || tab.uname,
          },
        },
        results: results.select { |result| unames_blocks.include?(result[:widget]) },
      }

      page_width, page_height = ImageExporters::DashboardsV4::Helper.extract_export_page_width_and_height(@dashboard, tab)

      [renderer_input, page_width, page_height]
    end

    sig { returns(T.untyped) }
    def dashboard_definition_with_filter
      ImageExporters::DashboardsV4::Helper.dashboard_definition_with_filter(@dashboard, @widget_viz_conditions_map)
    end

    sig { params(name: String).returns(String) }
    def normalize_file_name(name)
      name.parameterize
    end
  end
end

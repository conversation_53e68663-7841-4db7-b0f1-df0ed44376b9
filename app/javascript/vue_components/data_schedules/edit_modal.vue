<!--<docs>
  Use 'ui/modals/data_schedule_edit' to open this modal
</docs>-->

<template>
  <validation-form
    v-slot="{ meta, handleSubmit }"
    as="div"
  >
    <h-modal
      class="data-schedule-edit-modal"
      @dismiss="reject"
    >
      <template #title>
        {{ destTypeConfig.displayName }} Schedules
        <docs-popover
          v-if="destTypeConfig.docUrl"
          :url="destTypeConfig.docUrl"
        >
          Schedule sending reports/dashboards via {{ destTypeConfig.displayName }}
        </docs-popover>
      </template>
      <!-- not supporting swiching dest type yet -->
      <div
        v-if="false"
        class="row"
      >
        <div class="col-sm-2">
          <label class="control-label">Destination</label>
        </div>
        <div class="col-sm-10">
          <div class="schedule-types">
            <div
              v-for="config in destTypeConfigList"
              :key="config.type"
              @click="changeDestType(config)"
            >
              <h-icon :name="config.icon" />
              <label>{{ config.displayName }}</label>
            </div>
          </div>
        </div>
      </div>

      <div v-if="loading">
        <h-icon
          name="circle-notch"
          spin
        /> Loading...
      </div>
      <div v-else>
        <!-- Schedule Title Input -->
        <div class="row h-form-group">
          <div class="col-sm-2 h-form-label pt-2">
            <span>Schedule title</span>
          </div>
          <div class="col-sm-10">
            <input
              v-model="mutatedDataSchedule.title"
              class="h-input h-form-input"
              type="text"
              placeholder="A short description of this schedule"
            >
          </div>
        </div>

        <component
          :is="destTypeConfig.destForm"
          ref="dest-form"
          v-model="mutatedDataSchedule.dest"
          :data-schedule="mutatedDataSchedule"
          @dismiss="$emit('reject')"
        >
          <template #schedule>
            <div class="row h-form-group">
              <div class="col-sm-2 h-form-label pt-2">
                <span>Schedule</span>
              </div>
              <div class="col-sm-10">
                <schedule-selector
                  :schedule="mutatedDataSchedule.schedule"
                  :timezone="timezone"
                  @handle="handleSchedule"
                />
              </div>
            </div>
          </template>
          <template #filters>
            <div class="row panel-filters">
              <div class="col-sm-2 h-form-label">
                <span>Filter Values</span>
              </div>
              <div class="col-sm-10">
                <filter-value-list
                  v-model="mutatedDataSchedule.filter_values"
                  should-get-filter-values-from-selected-values
                  :filterable="false"
                  :source-info="sourceInfo"
                  @filter-loaded="onFilterLoaded"
                />
              </div>
            </div>
          </template>
        </component>
      </div>
      <template #footer>
        <span
          v-if="testExecutionEnabled"
          class="!float-left"
        >
          <HTooltip
            :content="'Execute this schedule with your current editings'"
            placement="top"
          >
            <HButton
              type="tertiary-highlight"
              class="ci-test-run-es"
              :disabled="!filterLoaded"
              @click="testExecute()"
            >
              Test Run
            </HButton>
          </HTooltip>
          <a
            v-if="testingStatus"
            class="test-status ci-test-status ml-2"
            @click="showTestJobLog"
          >
            <job-status :status="testingStatus" />
          </a>
        </span>
        <HButton
          type="secondary-default"
          @click="cancel()"
        >
          Cancel
        </HButton>
        <HButton
          type="primary-highlight"
          class="ci-es-done ml-1"
          :disabled="!filterLoaded || isExceedTenantUsage || !meta.valid"
          :icon="submitting ? 'loading' : undefined"
          :icon-spin="submitting"
          @click="handleSubmit($events, submit)"
        >
          Save
        </HButton>
      </template>
    </h-modal>
  </validation-form>
</template>

<script>
import { HTooltip, HButton } from '@holistics/design-system';
import { mapGetters } from 'vuex';
import { Form as ValidationForm } from 'vee-validate';
import {
  get, find, assign, noop, each, cloneDeep,
} from 'lodash';
import ScheduleSelector from '@/modules/DataSchedule/components/ScheduleSelector.vue';
import EmailSchedules from '@/es6/email_schedules';
import { DEST_LIST } from '@/modules/DataSchedule/constants/dests';
import filterValueList from '@/vue_components/filter_value_list.vue';
import * as Notifier from '@/core/services/notifier';
import * as Ajax from '@/core/services/ajax';
import Jobs from '@/jobs/jobs';
import jobStatus from '@/vue_components/job_status.vue';
import * as FeatureToggle from '@/core/services/featureToggle';
import JobLogModal from '@/ui/modals/job_log';
import DocsPopover from '@/core/components/ui/DocsPopover.vue';
import Schedule from '@/es6/schedule';
import usageReminderModal from '@/modules/AppAlerts/services/modals/usageReminder.modal';
import { isActiveJob } from '@/modules/Jobs/helpers/checkStatus';
import sftpDestForm from './destinations/sftp_dest_form.vue';
import adls2DestForm from './destinations/adls2_dest_form.vue';
import gsheetDestForm from './destinations/gsheet_dest_form.vue';
import slackDestForm from './destinations/slack_dest_form.vue';
import emailDestForm from './destinations/email_dest_form.vue';

const CLIENT_TIMEZONE = Schedule.getBrowserTimeZone();

export default {
  components: {
    HTooltip,
    HButton,
    emailDestForm,
    slackDestForm,
    gsheetDestForm,
    sftpDestForm,
    adls2DestForm,
    ScheduleSelector,
    filterValueList,
    jobStatus,
    DocsPopover,
    ValidationForm,
  },
  props: {
    dataSchedule: {
      type: Object,
      default: null,
    },
    source: {
      type: Object,
      default: null,
    },
  },
  emits: ['dismiss', 'resolve', 'reject'],
  data () {
    const destTypeConfigList = DEST_LIST;
    return {
      loading: false,
      destTypeConfigList,
      mutatedDataSchedule: null,
      destTypeConfig: destTypeConfigList[0],
      mutatedSchedule: {},
      filterLoaded: false,
      submitting: false,
      sourceInfo: {},
      testJobId: null,
      testingStatus: '',
    };
  },
  computed: {
    ...mapGetters('tenantSubscription', {
      isExceedTenantUsage: 'isExceedTenantUsage',
    }),
    isEditing () {
      return this.dataSchedule && !!this.dataSchedule.id;
    },
    timezone () {
      return CLIENT_TIMEZONE;
    },
    testExecutionEnabled () {
      return FeatureToggle.check('data_schedules:test_execution');
    },
  },
  created () {
    this.init();
  },
  methods: {
    async init () {
      this.loading = true;
      this.initDestTypeConfig();
      this.mutatedDataSchedule = await this.prepareDataSchedule();
      this.loading = false;
    },
    initDestTypeConfig () {
      if (this.dataSchedule && this.dataSchedule.dest_type) {
        this.destTypeConfig = find(this.destTypeConfigList, config => config.type === this.dataSchedule.dest_type);
      }
    },
    async prepareDataSchedule () {
      let dataSchedule = {};
      if (this.isEditing) {
        try {
          dataSchedule = await EmailSchedules.get(this.dataSchedule.id);
          this.sourceInfo = { id: dataSchedule.source_id, type: dataSchedule.source_type };
        } catch (err) {
          Ajax.handleAjaxError(err, 'Error getting Email Schedule from server');
        }
      } else {
        try {
          dataSchedule = this.dataSchedule || {};
          dataSchedule.source = await EmailSchedules.getSource(this.source.source_id, this.source.source_type);
          assign(dataSchedule, this.source);
          dataSchedule.dest_type = this.destTypeConfig.type;
          this.sourceInfo = { id: this.source.source_id, type: this.source.source_type };
        } catch (err) {
          Ajax.handleAjaxError(err, 'Error getting source object');
        }
      }
      // fill the data schedule missing fields with default values
      return EmailSchedules.new(dataSchedule);
    },
    changeDestType (config) {
      this.destTypeConfig = config;
    },
    handleSchedule (schedule) {
      this.mutatedSchedule = schedule;
    },
    cancel () {
      this.$modal.confirm('Discard changes?', 'All your changes will be lost.')
        .then(confirmed => {
          if (!confirmed) {
            return;
          }
          this.$emit('reject');
        }).catch(noop);
    },
    getSubmittingDs () {
      const ds = cloneDeep(this.mutatedDataSchedule);
      ds.schedule = cloneDeep(this.mutatedSchedule);
      each(ds.filter_values, fv => {
        fv.possibly_outdated = false;
      });
      return ds;
    },
    async validateForm () {
      const destForm = this.$refs['dest-form'];
      if (destForm.validate) {
        return destForm.validate();
      }
      return true;
    },
    async submit () {
      if (this.isExceedTenantUsage) {
        usageReminderModal();
        return;
      }

      if (!this.filterLoaded) return;
      if (this.submitting) return;
      this.submitting = true;

      const validated = await this.validateForm();
      if (!validated) {
        this.submitting = false;
        return;
      }

      const ds = this.getSubmittingDs();

      const action = this.isEditing ? 'update' : 'add';
      const actioned = this.isEditing ? 'updated' : 'added';
      const typeName = this.destTypeConfig.displayName;
      const successMsg = `${typeName} Schedule successfully ${actioned}`;
      const errorMsg = `Failed to ${action} new ${typeName} Schedule`;
      try {
        if (!this.isEditing) {
          await EmailSchedules.create(ds);
        } else {
          await EmailSchedules.update(ds);
        }
        Notifier.success(successMsg);
        this.$emit('resolve', null);
      } catch (err) {
        Ajax.handleAjaxError(err, errorMsg);
      }

      this.submitting = false;
    },
    async testExecute () {
      if (!this.filterLoaded) return;
      if (isActiveJob(this.testingStatus)) return;

      const validated = await this.validateForm();
      if (!validated) return;

      const confirmed = await this.$modal.confirm('Run test now?', 'This schedule will be sent immediately.', { confirmText: 'Run test' });
      if (!confirmed) return;

      this.testingStatus = 'submitting';
      let jobInfo = null;
      try {
        jobInfo = await this.submitTestExecute();
      } catch (err) {
        this.testingStatus = Ajax.errorMessageFromAjax(err);
      }

      if (jobInfo) {
        this.testJobId = jobInfo.job_id;
        this.pollTestJob(jobInfo);
      }
    },
    pollTestJob (jobInfo) {
      Jobs.pollResults(jobInfo, {
        getResultsFunc: () => Promise.resolve({ status: 'success' }),
        successFunc: noop,
        errorFunc: (err) => { this.testingStatus = Ajax.errorMessageFromAjax(err); },
        cacheFunc: noop,
        statusFunc: ({ status }) => { this.testingStatus = status; },
      });
    },
    showTestJobLog () {
      if (!this.testJobId) return;
      JobLogModal.open(this.testJobId);
    },
    getTestingDs () {
      const ds = this.getSubmittingDs();
      ds.id = null;
      ds.dest.id = null;
      ds.schedule.id = null;
      return ds;
    },
    submitTestExecute () {
      const ds = this.getTestingDs();
      const originalDsId = get(this.dataSchedule, 'id', null);
      return EmailSchedules.testExecute(ds, originalDsId);
    },
    onFilterLoaded () {
      this.filterLoaded = true;
    },
    reject () {
      this.$emit('reject');
    },
  },
};
</script>

<style lang="scss">
  .data-schedule-edit-modal {
    .filter-value-wrapper {
      margin-bottom: 10px;
      fieldset.field-unit {
        margin-top: 0;
      }
    }
    a.test-status {
      text-decoration: none
    }
  }
</style>

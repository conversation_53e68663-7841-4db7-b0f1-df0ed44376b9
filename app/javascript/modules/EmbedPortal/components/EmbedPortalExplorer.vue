<template>
  <div class="embed-portal-explorer-wrapper bg-gray-50">
    <div class="flex flex-row space-x-1">
      <SearchBox
        v-model="searchText"
        class="w-full"
        placeholder="Search"
        clearable
        @update:model-value="onSearchTextChange"
      />
    </div>
    <div
      class="mt-2"
    >
      <!--
        @update:selected-keys="noop":
        Already handled selected key changes by URL in useEmbedPortalNodes (onBeforeRouteUpdate)
        instead of handling here due to some navigations don't come from clicking on the tree nodes (e.g. self-navigations, host control navigations)
      -->
      <HTree
        v-model:expanded-keys="filteredExpandedKeys"
        :nodes="filteredNodes"
        click-mode="expand"
        :node-indent="12"
        :selected-keys="selectedKey ? [selectedKey] : []"
        :context-menu-options="buildNodeContextMenu"
        class="embed-portal-tree overflow-auto"
        @update:expanded-keys="updateExpandedKeys"
        @update:selected-keys="noop"
      >
        <template #node-content="{ node }">
          <div
            class="hui-tree-node-content flex flex-1 items-center overflow-hidden"
            :data-value="node.key"
          >
            <HIcon
              v-if="node.icon"
              :name="node.icon"
              class="px-0.5"
            />
            <div class="truncate px-0.5 no-underline">
              <HTextHighlight
                :text="node.label"
                :highlights="node.isLeaf ? searchText : ''"
              />
            </div>
          </div>
        </template>
        <template #node-append="{ node, onContextMenu }">
          <HIcon
            v-if="node.isDeleting"
            class="h-4 px-0.5"
            data-ci="embed-portal-loading-node"
            spin
            name="loading"
          />
          <div
            v-else
            class="h-4 px-0.5"
          >
            <HIcon
              v-if="node.permission.canCreateChildren"
              name="add-circle"
              :data-ci="`button-create-${node.key}-dashboard`"
              @click.stop="() => goToDashboardCreationPage(node.key === PERSONAL_WORKSPACE_NODE_KEY)"
            />
            <HIcon
              v-else-if="hasNodeMenuAction(node)"
              name="ellipsis-horizontal"
              class="embed-portal-node-action"
              data-ci="more-embed-portal-node-action"
              @click.stop="onContextMenu"
            />
            <div
              v-else-if="[NEW_ORG_DASHBOARD_NODE_KEY as string, NEW_PERSONAL_DASHBOARD_NODE_KEY as string].includes(node.key)"
              class="flex h-4 items-center"
            >
              <span class="block size-1.5 items-center rounded-full bg-orange-500" />
            </div>
          </div>
        </template>
      </HTree>
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  HIcon, HTextHighlight, HTree, type TreeNodeKey,
} from '@holistics/design-system';
import { ref, watch } from 'vue';
import SearchBox from '@/core/components/ui/SearchBox.vue';
import { noop } from 'lodash';
import { useEmbedPortalContextMenu } from '@/modules/EmbedPortal/composables/useEmbedPortalContextMenu';
import type { EmbedObjectTreeNode } from '../types';
import { searchTreeNodes } from '../helpers/searchTreeNodes';
import {
  DATASET_NODE_KEY,
  ORG_WORKSPACE_NODE_KEY,
  PERSONAL_WORKSPACE_NODE_KEY,
  PREBUILT_DASHBOARD_NODE_KEY,
  NEW_ORG_DASHBOARD_NODE_KEY,
  NEW_PERSONAL_DASHBOARD_NODE_KEY,
} from '../helpers/buildEmbedPortalTreeNodes';
import { useEmbedPortalNodes } from '../composables/useEmbedPortalNodes';

const {
  nodes: originalNodes, selectedKey, goToDashboardCreationPage, goToNode, deleteDashboardNode,
} = useEmbedPortalNodes();

const { buildNodeContextMenu, hasNodeMenuAction } = useEmbedPortalContextMenu({
  deleteDashboardFn: deleteDashboardNode,
  createDashboardFn: goToDashboardCreationPage as (isPersonal: boolean) => Promise<void>,
});

const searchText = ref('');

const filteredNodes = ref<EmbedObjectTreeNode[]>([]);
const originalExpandedKeys = ref<TreeNodeKey[]>([PREBUILT_DASHBOARD_NODE_KEY, ORG_WORKSPACE_NODE_KEY, PERSONAL_WORKSPACE_NODE_KEY, DATASET_NODE_KEY]);
const filteredExpandedKeys = ref<TreeNodeKey[]>(originalExpandedKeys.value);

function updateExpandedKeys (keys: TreeNodeKey[]) {
  if (searchText.value.trim() === '') {
    originalExpandedKeys.value = keys;
  }
}

function onSearchTextChange (searchString: string) {
  if (searchString.trim() === '') {
    filteredNodes.value = originalNodes.value;
    filteredExpandedKeys.value = originalExpandedKeys.value;
    return;
  }

  // @ts-ignore
  const searchResult = searchTreeNodes(originalNodes.value, searchString, (node: EmbedObjectTreeNode, text: string) => {
    return !!node.isLeaf && node.label.toLowerCase().includes(text.toLowerCase());
  });

  filteredNodes.value = searchResult.nodes;
  filteredExpandedKeys.value = searchResult.expandedKeys;
}

watch(originalNodes, () => {
  onSearchTextChange(searchText.value);
}, { immediate: true });

defineExpose({ goToNode });
</script>

<style lang="postcss">
.embed-portal-tree {
  .hui-tree-node-container .embed-portal-node-action {
    @apply hidden
  }

  .hui-tree-node-container:hover .embed-portal-node-action {
    @apply inline-block
  }
}
</style>

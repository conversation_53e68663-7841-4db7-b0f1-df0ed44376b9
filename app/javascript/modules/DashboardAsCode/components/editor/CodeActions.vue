<template>
  <div
    class="flex"
    :class="{ 'flex-col space-y-2': vertical, 'space-x-0.5': !vertical }"
  >
    <template v-if="vertical">
      <HTooltip
        content="Add visualization"
        placement="right"
      >
        <HButton
          type="tertiary-default"
          unified
          data-ci="add-viz-block"
          :disabled="disabled"
          @click="addVizBlock"
        >
          <ColoredVizIcon />
        </HButton>
      </HTooltip>
      <HTooltip
        v-if="blockLibraryEnabled && !!fetchTemplatesFunc"
        content="Block library"
        placement="right"
      >
        <BlockTemplates
          :fetch-templates-func="fetchTemplatesFunc"
          vertical
          @add="addTemplateBlock"
        />
      </HTooltip>
      <HTooltip
        content="Add text"
        placement="right"
      >
        <HButton
          type="tertiary-default"
          icon="text"
          unified
          data-ci="add-text-block"
          :disabled="disabled"
          @click="addTextBlock"
        />
      </HTooltip>
      <HTooltip
        content="Add filter"
        placement="right"
      >
        <HButton
          type="tertiary-default"
          unified
          icon="filter"
          data-ci="add-ic-block"
          :disabled="disabled"
          @click="addIcBlock()"
        />
      </HTooltip>
      <HTooltip
        content="Add period comparison"
        placement="right"
      >
        <HButton
          type="tertiary-default"
          unified
          icon="calendar-line-light"
          data-ci="add-pop-block"
          :disabled="disabled"
          @click="addIcBlock('pop')"
        />
      </HTooltip>
      <HTooltip
        content="Add date drill"
        placement="right"
      >
        <HButton
          type="tertiary-default"
          unified
          icon="calendar-settings-light"
          data-ci="add-date-drill-block"
          :disabled="disabled"
          @click="addIcBlock('date_drill')"
        />
      </HTooltip>
      <GridSettings
        vertical
        :disabled="disabled"
      />
      <AiCodeGen
        v-if="aiCodeGenEnabled"
        :uname="definition.uname"
        :fetch-dataset-service="fetchDatasetService"
        @generated="onAiGeneratedDef"
      />
    </template>
    <template v-else>
      <HTooltip
        content="Add visualization"
        placement="bottom"
      >
        <HButton
          type="tertiary-default"
          data-ci="add-viz-block"
          :disabled="disabled"
          @click="addVizBlock"
        >
          <div class="flex flex-row items-center">
            <ColoredVizIcon />
            <span class="ml-1">Visualization</span>
          </div>
        </HButton>
      </HTooltip>
      <HTooltip
        v-if="blockLibraryEnabled && !!fetchTemplatesFunc"
        content="Block library"
        placement="bottom"
      >
        <BlockTemplates
          :fetch-templates-func="fetchTemplatesFunc"
          @add="addTemplateBlock"
        />
      </HTooltip>
      <HTooltip
        content="Add text"
        placement="bottom"
      >
        <HButton
          type="tertiary-default"
          icon="text"
          data-ci="add-text-block"
          :disabled="disabled"
          @click="addTextBlock"
        >
          Text
        </HButton>
      </HTooltip>
      <HTooltip
        content="Add filter"
        placement="bottom"
      >
        <HButton
          type="tertiary-default"
          icon="filter"
          data-ci="add-ic-block"
          :disabled="disabled"
          @click="addIcBlock()"
        >
          Filter
        </HButton>
      </HTooltip>
      <HDropdown
        :options="[
          {
            key: 'pop',
            label: 'Period comparison',
            icons: 'calendar-line-light',
            class: 'ci-add-pop',
            action: () => addIcBlock('pop')
          },
          {
            key: 'date-drill',
            label: 'Date drill',
            icons: 'calendar-settings-light',
            class: 'ci-add-date-drill',
            action: () => addIcBlock('date_drill')
          },
        ]"
      >
        <HButton
          type="tertiary-default"
          icon="add"
          icon-right="caret-down"
          unified
          data-ci="add-others"
          :disabled="disabled"
        >
          Controls
        </HButton>
      </HDropdown>
      <GridSettings
        :disabled="disabled"
      />
      <AiCodeGen
        v-if="aiCodeGenEnabled"
        :uname="definition.uname"
        :fetch-dataset-service="fetchDatasetService"
        @generated="onAiGeneratedDef"
      />
    </template>
  </div>
</template>

<script setup lang="ts">
import { computed, defineAsyncComponent } from 'vue';
import type {
  DashboardDefinition, DashboardInteraction, DatasetWithModels, DashboardUpdateEvent, BlockTemplate,
} from '@holistics/aml-std';
import { HTooltip, HButton, HDropdown } from '@holistics/design-system';
import type { FilterType } from '@/modules/DynamicFilters/constants';
import { useDashboardConfigs } from '@/modules/DashboardAsCode/composables/useDashboardConfigs';
import { generateInteractionsForNewBlocks } from '@/modules/DashboardAsCode/utils/interactions';
import { check as checkFT } from '@/core/services/featureToggle';
import ColoredVizIcon from './CodeActions/ColoredVizIcon.vue';
import { useDashboardDatasets } from '../../composables/useDashboardDatasets';
import { useDashboardFilters } from '../../composables/useDashboardFilters';
import { useDashboardTimezone } from '../../composables/useDashboardTimezone';
import { useDashboard } from '../../composables/useDashboard';
import { buildBlockPermissionMap, buildVizConditionables } from '../../utils/buildFilterConditionables';
import { textBlockModal } from '../../services/textBlock.modal';
import { vizBlockModal } from '../../services/vizBlock.modal';
import { filterBlockModal } from '../../services/filterBlock.modal';
import { isFilterBlock } from '../../utils/blocks';
import { findDatasetIds } from '../../utils/findDatasetReferences';
import { generatePositionEvent } from '../../utils/layouts';
import BlockTemplates from './BlockTemplates/BlockTemplates.vue';
import { generateAddTemplateBlockEvent } from '../../utils/generateTemplateBlockCode';
import GridSettings from './GridSettings.vue';

const AiCodeGen = defineAsyncComponent(() => import('./AiCodeGen/AiCodeGen.vue'));

const props = defineProps<{
  definition: DashboardDefinition
  vertical?: boolean
  disabled?: boolean
}>();
const emit = defineEmits<{(e: 'events', payload: DashboardUpdateEvent[]): void }>();

const { datasets, rawDatasets, fetchDatasets } = useDashboardDatasets();
const {
  currentTimezone,
} = useDashboardTimezone();
const {
  fetchDatasetService, extraDetails, fetchAmlBindingFunc, fetchTemplatesFunc,
} = useDashboardConfigs();
const { filters } = useDashboardFilters();
const { projectId, inDevMode } = extraDetails;

const existingUnames = computed(() => props.definition.blocks.map(b => b.uname));
const usableDatasetId = computed(() => {
  const datasetIds = findDatasetIds(props.definition);
  return datasetIds.length !== 1 ? undefined : datasetIds[0];
});

const aiCodeGenEnabled = checkFT('dashboards_v4:ai_codegen');
const autoPlacementForNewBlockEnabled = checkFT('dashboards_v4:auto_placement_for_new_block');
const blockLibraryEnabled = checkFT('dashboard_v4:block_library');

const { dashboard, currentLayout } = useDashboard();

async function addTextBlock () {
  const result = await textBlockModal({
    existingUnames: existingUnames.value,
  });
  if (result.status === 'resolved') {
    const updateBlockEvents: DashboardUpdateEvent[] = [{
      event: 'UpdateBlock',
      uname: result.data.uname,
      block: result.data,
    }];

    if (autoPlacementForNewBlockEnabled && currentLayout.value) {
      const positionEvent = generatePositionEvent(currentLayout.value, result.data);
      if (positionEvent) {
        updateBlockEvents.push(positionEvent);
      }
    }
    emit('events', updateBlockEvents);
  }
}

async function addVizBlock () {
  const result = await vizBlockModal({
    projectId,
    inDevMode,
    fetchDatasetService,
    existingUnames: existingUnames.value,
    fetchAmlBindingFunc,
    targetDatasetId: usableDatasetId.value,
    timezone: currentTimezone.value,
  });
  if (result.status === 'resolved') {
    await fetchDatasets([result.data.viz.dataset_id as number]);

    const block = result.data;

    const newInteractions: DashboardInteraction[] = generateInteractionsForNewBlocks([block], props.definition, currentLayout.value?.uname);

    const updateBlockEvents: DashboardUpdateEvent[] = [{
      event: 'UpdateBlock',
      uname: block.uname,
      block,
      datasets: datasets.value,
    }, {
      event: 'UpdateInteractions',
      interactions: props.definition.interactions.concat(...newInteractions),
      datasets: datasets.value,
    }];

    if (autoPlacementForNewBlockEnabled && currentLayout.value) {
      const positionEvent = generatePositionEvent(currentLayout.value, result.data);
      if (positionEvent) {
        updateBlockEvents.push(positionEvent);
      }
    }

    emit('events', updateBlockEvents);
  }
}

async function addIcBlock (filterType?: FilterType) {
  const result = await filterBlockModal({
    projectId,
    fetchDatasetService,
    existingUnames: existingUnames.value,
    vizConditionables: buildVizConditionables(props.definition, {
      datasets: rawDatasets.value,
      filterType,
      blockPermissionMap: buildBlockPermissionMap(dashboard.value),
    }),
    filterType,
    filters: filters.value,
    fetchAmlBindingFunc,
  });
  if (result.status === 'resolved') {
    const { block, interactions } = result.data;
    if (isFilterBlock(block)) {
      const { data_set_id: datasetId } = (block.filter?.filter_source as any || {});
      if (datasetId) {
        await fetchDatasets([datasetId]);
      }
    }

    // filter block
    const updateBlockEvents: DashboardUpdateEvent[] = [{
      event: 'UpdateBlock',
      uname: block.uname,
      block,
      datasets: datasets.value,
    }, {
      event: 'UpdateInteractions',
      interactions: props.definition.interactions.concat(...interactions),
      datasets: datasets.value,
    }];

    if (autoPlacementForNewBlockEnabled && currentLayout.value) {
      const positionEvent = generatePositionEvent(currentLayout.value, block);
      if (positionEvent) {
        updateBlockEvents.push(positionEvent);
      }
    }

    emit('events', updateBlockEvents);
  }
}

function onAiGeneratedDef (payload: { definition: DashboardDefinition, dataset: DatasetWithModels }) {
  emit('events', [{
    event: 'UpdateDefinition',
    definition: payload.definition,
    datasets: [payload.dataset],
  }]);
}

function addTemplateBlock ({ template, params }: { template: BlockTemplate, params: Record<string, any> }) {
  const events = generateAddTemplateBlockEvent(template, params, {
    definition: dashboard.value.definition,
    currentLayout: currentLayout.value,
  });
  emit('events', events);
}
</script>

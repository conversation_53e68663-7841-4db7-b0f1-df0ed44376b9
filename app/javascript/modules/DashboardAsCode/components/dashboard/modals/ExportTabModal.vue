<template>
  <HModal
    v-model:shown="shown"
    size="md"
    :title="'Export specific tabs'"
    prevent-click-outside
    prevent-press-escape
    prevent-close-animation-disabled
    :resolve-button="{
      label: 'Export',
      disabled,
      type: 'primary-highlight',
    }"
    data-ci="ci-export-tab-modal"
    @resolve="resolve"
  >
    <div class="flex flex-col gap-2">
      <span class="text-sm">Select which dashboard tabs to include:</span>
      <HCheckbox
        v-for="option in options"
        :key="option.key"
        class="flex items-center"
        :model-value="selectedOptions[option.key]"
        :data-ci="`ci-export-tab-checkbox-${option.key}`"
        @update:model-value="(value: boolean) => selectedOptions[option.key] = value"
      >
        <span class="text-sm">{{ option.label }}</span>
      </HCheckbox>
    </div>
  </HModal>
</template>
<script setup lang="ts">
import {
  HModal, HCheckbox,
} from '@holistics/design-system';
import { computed, ref } from 'vue';

const props = withDefaults(defineProps<{
  options: {
    label: string,
    key: string,
  }[],
}>(), {});

const shown = defineModel<boolean>('shown', { default: false });

const emit = defineEmits<{(e: 'resolve', value: string[]): void,
}>();

const selectedOptions = ref(props.options.reduce((acc, option) => {
  acc[option.key] = true;
  return acc;
}, {} as Record<string, boolean>));

const resolve = () => {
  emit('resolve', Object.keys(selectedOptions.value).filter((key) => selectedOptions.value[key]));
};

const disabled = computed(() => {
  return Object.values(selectedOptions.value).every((value) => !value);
});

</script>

<template>
  <div class="flex size-full flex-col">
    <PreviewEmbedWarning
      v-if="!hideEmbedWarning"
      object-label="dashboard"
    />
    <div
      v-h-loading.body="isFetchingDashboard"
      data-ci="embed-portal-dashboard"
      class="flex-1"
    >
      <Dashboard
        v-if="dashboard"
        :key="dashboard.id"
        :dashboard="dashboard"
        :hide-header="embedSettings.hide_header_panel"
        :enable-embed-export="embedSettings.enable_dashboard_export"
        :class="DAC_THEME_SCOPE_CSS_CLASS"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  computed, onMounted, ref,
} from 'vue';
import { useTitle } from '@vueuse/core';
import { useRoute } from 'vue-router';
import { DAC_THEME_SCOPE_CSS_CLASS } from '@holistics/aml-std';
import { fetchEmbedDashboard } from '@/modules/DynamicDashboards/services/dashboards.ajax';
import PreviewEmbedWarning from '@/core/components/ui/banner/PreviewEmbedWarning.vue';
import Dashboard from '../components/dashboard/Dashboard.vue';
import { DashboardAsCode } from '../types';

const { hideEmbedWarning = false } = defineProps<{
  hideEmbedWarning?: boolean;
}>();

const dashboard = ref<null | DashboardAsCode>(null);
const isFetchingDashboard = ref(false);

const embedSettings = window.H.embed_settings;

onMounted(async () => {
  const route = useRoute();
  const hashcode = String(route.params.hashcode);
  const dashboardId = route.params.dashboard_id;
  isFetchingDashboard.value = true;
  const res = await fetchEmbedDashboard(hashcode, dashboardId);
  isFetchingDashboard.value = false;
  dashboard.value = res;
});

const pageTitle = computed(() => dashboard.value?.definition.title || 'Loading dashboard...');
useTitle(pageTitle, {
  titleTemplate: '%s - Holistics',
});
</script>

import type {
  CanvasLayout, DashboardLayout, Position,
  DashboardBlock, BlockPosition,
  UpdateCanvasBlockPositionEvent,
  DashboardDefinition,
  TabLayout,
  LinearLayout,
} from '@holistics/aml-std';
import { cloneDeep, uniq } from 'lodash';
import { IS_MOBILE } from '../constants/views';

const DUPLICATED_BLOCK_DISTANCE = 50;

// precise: one block must really overlap with other
export function isOverlapped (p1: Position, p2: Position, precise = false) {
  const compare = precise
    ? (a: number, b: number) => a <= b
    : (a: number, b: number) => a < b;
  // If one rectangle is above other
  if (compare(p1.y + p1.h, p2.y) || compare(p2.y + p2.h, p1.y)) {
    return false;
  }
  // If one rectangle is on left side of other
  if (compare(p1.x + p1.w, p2.x) || compare(p2.x + p2.w, p1.x)) {
    return false;
  }

  return true;
}

export function checkPositionAlignments (source: Position, target: Position) {
  const verticals = [source.y, source.y + source.h / 2, source.y + source.h];
  const horizontals = [source.x, source.x + source.w / 2, source.x + source.w];
  return {
    top: verticals.some(v => v === target.y),
    middle: verticals.some(v => v === target.y + target.h / 2),
    bottom: verticals.some(v => v === target.y + target.h),
    left: horizontals.some(v => v === target.x),
    center: horizontals.some(v => v === target.x + target.w / 2),
    right: horizontals.some(v => v === target.x + target.w),
  };
}

export function findOverlappingBlockPositions (layout: CanvasLayout, blockUname: string) {
  const targetBlock = layout.blocks[blockUname];
  if (!targetBlock) {
    return [];
  }
  return Object.entries(layout.blocks).filter(([uname, position]) => {
    return uname !== blockUname && isOverlapped(position.position, targetBlock.position, true);
  }).map(([, position]) => position);
}

export function findGroupOverlappingBlockPositions (layout: CanvasLayout, groupItems: string[]) {
  const targetBlocks = groupItems.map(uname => layout.blocks[uname]).filter(b => !!b);
  if (targetBlocks.length === 0) {
    return [];
  }
  return Object.entries(layout.blocks).filter(([uname, blockPosition]) => {
    return !groupItems.includes(uname)
    && targetBlocks.some(({ position }) => isOverlapped(blockPosition.position, position, true));
  }).map(([, position]) => position);
}

export interface LayoutInfo {
  currentLayout: CanvasLayout | LinearLayout;
  parentLayout?: TabLayout;
}
export function getLayoutInfo (definition?: DashboardDefinition, currentTabUname?: string): LayoutInfo | undefined {
  // currently there are only 1 views => get the first one
  // TODO: update this once we implement multiple views
  const layout = (definition?.views ?? [])[0];
  if (!layout) {
    return undefined;
  }

  if (layout.type === 'TabLayout') {
    const currentTab = layout.tabs.find(tab => tab.uname === currentTabUname) ?? layout.tabs[0];
    return {
      currentLayout: currentTab,
      parentLayout: layout,
    };
  }

  return {
    currentLayout: layout,
  };
}

function _generateDefaultBlockPosition (layout: CanvasLayout, block: DashboardBlock) {
  switch (block.type) {
    case 'VizBlock':
      if (block.viz.viz_setting.viz_type === 'metric_kpi') {
        return {
          position: {
            x: 0,
            y: 0,
            w: 250,
            h: 170,
          },
          layer: 0,
        };
      }
      return {
        position: {
          x: 0,
          y: 0,
          w: Math.min(layout.width / 2, 400),
          h: Math.min(layout.width / 3, 300),
        },
        layer: 0,
      };
    case 'FilterBlock':
    case 'PopBlock':
    case 'DateDrillBlock':
      return {
        position: {
          x: 0,
          y: 0,
          w: 300,
          h: 100,
        },
        layer: 0,
      };
    case 'TextBlock':
      // eslint-disable-next-line no-case-declarations
      const lines = block.content.split('\n').filter(l => l.length > 0);
      // below is just a simple heuristic method to estimate text widget size
      // it's limited by the layout size anw
      // TODO: find a better algorithm if needed
      return {
        position: {
          x: 0,
          y: 0,
          w: Math.min(layout.width / 2, Math.max(...lines.map(l => l.length)) * 13 + 10),
          h: Math.min(layout.width / 3, lines.length * 30 + 20),
        },
        layer: 0,
      };
    default: {
      return {
        position: {
          x: 0,
          y: 0,
          w: Math.min(layout.width / 2, 400),
          h: Math.min(layout.width / 3, 300),
        },
        layer: 0,
      };
    }
  }
}

export function generateDefaultBlockPosition (layout: CanvasLayout, block: DashboardBlock) {
  const position = _generateDefaultBlockPosition(layout, block);

  // ensure block width and height align with grid size
  const gridSize = layout.grid_size ?? 10;
  return {
    position: {
      x: position.position.x,
      y: position.position.y,
      w: Math.ceil(position.position.w / gridSize) * gridSize,
      h: Math.ceil(position.position.h / gridSize) * gridSize,
    },
    layer: position.layer,
  };
}

export function generateDuplicatedBlockPosition (layout: CanvasLayout, originalBlock: DashboardBlock) {
  const originalPosition = layout.blocks[originalBlock.uname];
  if (!originalPosition) {
    return generateDefaultBlockPosition(layout, originalBlock);
  }
  const newPosition = cloneDeep(originalPosition);
  newPosition.position.x += DUPLICATED_BLOCK_DISTANCE;
  newPosition.position.y += DUPLICATED_BLOCK_DISTANCE;

  return newPosition;
}

function isInTheSameRow (p1: Position, p2: Position) {
  if ((p1.y + p1.h <= p2.y) || (p2.y + p2.h <= p1.y)) {
    return false;
  }
  return true;
}

export function suggestPositionForBlock (layout: CanvasLayout, newBlockWidth: number, newBlockHeight: number): BlockPosition | null {
  const blockPositions = Object.values(layout.blocks).map(({ position }) => position);
  const layer = Object.values(layout.blocks).map(({ layer: layerBlock }) => layerBlock).reduce((a, b) => Math.max(a, b), 0) + 1;
  const padding = layout.grid_size ?? 10;
  const buffer = layout.height; // buffer height is double the height of the layout

  // add padding size
  const suggestedPos = {
    x: 0,
    y: 0,
    w: newBlockWidth + 2 * padding,
    h: newBlockHeight + 2 * padding,
  };

  while (suggestedPos.y + suggestedPos.h < layout.height + buffer) {
    // start finding on a new row => reset x
    suggestedPos.x = 0;

    const blocksInSameRow = blockPositions.filter(b => isInTheSameRow(b, suggestedPos)).sort((p1, p2) => (p1.x + p1.w) - (p2.x + p2.w));

    while (suggestedPos.x + suggestedPos.w <= layout.width || suggestedPos.x === 0) {
      const overlapped = blocksInSameRow.find(b => isOverlapped(b, suggestedPos, true));
      if (!overlapped) {
        return {
          // handle padding
          position: {
            x: suggestedPos.x + padding,
            y: suggestedPos.y + padding,
            w: suggestedPos.w - 2 * padding,
            h: suggestedPos.h - 2 * padding,
          },
          layer,
        };
      }
      // If overlapped, set x to the right of the overlapped block and continue check next column
      suggestedPos.x = overlapped.x + overlapped.w;
    }
    // After not found in the same row, set y to the bottom the min y of the blocks in the same row and continue check the next row
    suggestedPos.y = Math.min(...blocksInSameRow.map(b => b.y + b.h));
  }

  // can't suggest a valid position, put it to the top left corner
  return {
    position: {
      x: padding,
      y: padding,
      w: newBlockWidth,
      h: newBlockHeight,
    },
    layer,
  };
}

export function generatePositionEvent (
  currentLayout: DashboardLayout,
  block: DashboardBlock,
): UpdateCanvasBlockPositionEvent | null {
  if (currentLayout && currentLayout.type === 'CanvasLayout') {
    const { w: newWidth, h: newHeight } = generateDefaultBlockPosition(currentLayout, block).position;
    const newPosition = suggestPositionForBlock(currentLayout as CanvasLayout, newWidth, newHeight);

    if (newPosition) {
      return {
        event: 'UpdateCanvasBlockPosition',
        uname: currentLayout.uname,
        blockUname: block.uname,
        position: newPosition,
      };
    }
    return null;
  }
  return null;
}

export function sortMobileBlocks (definition: DashboardDefinition, layout: CanvasLayout, inclusive = false): DashboardBlock[] {
  const blocksWithPosition = definition.blocks.map(block => {
    const position = layout.blocks[block.uname];
    if (position) {
      return {
        block,
        position: position.position,
      };
    }
    if (!inclusive) {
      return {
        block,
        position: generateDefaultBlockPosition(layout, block).position,
      };
    }
    return null;
  }).filter(p => p !== null);

  return blocksWithPosition.sort((b1, b2) => {
    if (b1.position.y === b2.position.y) {
      return b1.position.x - b2.position.x;
    }

    return b1.position.y - b2.position.y;
  }).map(b => b.block);
}

export function getLayoutBlocks (layout: DashboardLayout): string[] {
  if (layout.type === 'TabLayout') {
    return uniq(layout.tabs.flatMap(tab => getLayoutBlocks(tab)));
  }
  if (layout.type === 'CanvasLayout') {
    return Object.keys(layout.blocks ?? {});
  }
  if (layout.type === 'LinearLayout') {
    return layout.blocks ?? [];
  }
  return [];
}

export function getRenderedBlocks (layout: CanvasLayout | LinearLayout) {
  // canvas in mobile view
  if (IS_MOBILE && layout.type === 'CanvasLayout' && layout.mobile?.mode === 'manual') {
    return (layout.mobile.blocks ?? []).map(b => b.uname);
  }

  return getLayoutBlocks(layout);
}

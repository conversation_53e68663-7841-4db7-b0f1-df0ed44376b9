/* eslint-disable class-methods-use-this */
import { nextTick } from 'vue';
import { isEqual, isPlainObject } from 'lodash';
import type { Operation } from 'fast-json-patch';
import eventBus, { GlobalEvents } from '@/core/services/eventBus';
import { check as checkFT } from '@/core/services/featureToggle';
import { DatabaseJobSourceType, RevampedJobSourceType } from '@/modules/Jobs/constants/jobSourceTypes';
import Jobs from '@/jobs/jobs';
import generateDocsLink from '@/modules/FeatureToggleGroup/utils/generateDocsLink';
import { IconName } from '@holistics/icon';
import AmlDataset from '@aml-studio/client/models/Dataset';
import { withinSpan } from '@/modules/HOtel/services/tracing';
import cleanUpSetting from '@/modules/Viz/utils/cleanUpSetting';
import convertSettingForBackend from '@/modules/Viz/utils/convertSettingForBackend';
import areVizSettingsEqual from '@/modules/Viz/utils/areVizSettingsEqual';
import type { Context, ContextExploreAmlDataset } from '../context';
import { submitChat } from '../ai.ajax';
import { buildVizSetting } from './Aqua/buildVizSetting';
import { ChatableAgent } from './BaseAgent';
import {
  AgentName, Answer, OnboardingItem, Suggestion,
} from './types';
import { FollowUpsParams, HOtelContext, RestorableStatus } from '../types';

function handleSugaAnswer (answer: string): Suggestion[] {
  let json: string;
  const matches = [...answer.matchAll(/```[\S]*[\s]+([\s\S]*?)```/g)];
  if (matches.length) {
    // NOTE: get last json
    json = matches[matches.length - 1][1].trim();
  } else {
    json = answer.trim();
  }

  if (!json) return [];

  const obj = JSON.parse(json) as { questions: Record<string, string[]> };
  if (!isPlainObject(obj)) return [];

  const suggestions: Suggestion[] = [];
  Object.keys(obj.questions).forEach((tag) => {
    obj.questions[tag].forEach(q => {
      suggestions.push({
        type: 'suggestion',
        label: q,
        tag,
        icon: 'lightbulb',
        buttonType: 'outline-highlight',
        onClick ({ chat }) {
          chat(q);
        },
      });
    });
  });
  return suggestions;
}

export class AquaAgent<C extends ContextExploreAmlDataset> extends ChatableAgent<C> {
  answerTicks?: number;

  get name (): AgentName {
    return 'aqua';
  }

  get label (): string {
    return 'Explore data (with thinking)';
  }

  get jobType (): DatabaseJobSourceType {
    return DatabaseJobSourceType['Ai::Agents::Aqua'];
  }

  get jobLabel (): RevampedJobSourceType {
    return RevampedJobSourceType.AIAqua;
  }

  isFtEnabled () {
    return checkFT('ai:aqua');
  }

  protected isAvailableInContext (context: Context): context is C {
    if (!checkFT('viz_setting:amql_filter_conditions')) return false;

    return context.type === 'explore_dataset' && (context.dataset instanceof AmlDataset || context.dataset === undefined);
  }

  protected usabilityInContext (context: C): { valid: boolean; reason: string; } {
    if (context.dataset.__engine__ !== 'aql' && !checkFT('data_models:aql_as_default') && !checkFT('data_models:aql_as_default_with_biz_cal')) {
      return {
        valid: false,
        reason: 'This mode only works with AQL datasets.',
      };
    }
    return {
      valid: true,
      reason: '',
    };
  }

  settingDescription () {
    return 'Help users explore data using natural language. Capable of analytical thinking and advanced calculations.';
  }

  description () {
    return 'Capable of analytical thinking and advanced calculations';
  }

  introduction () {
    return undefined;
  }

  placeholder () {
    return 'Ask a data question...';
  }

  docsLink (): string {
    return generateDocsLink('/docs/data-caching');
  }

  iconName (): IconName {
    return 'compass';
  }

  shouldFetchSuggestions ({ params, lastParams } : { params: FollowUpsParams<C>; lastParams: FollowUpsParams<C> | undefined; }): boolean {
    return !isEqual(params.conversation, lastParams?.conversation)
            || params.context.datasetId !== lastParams?.context?.datasetId;
  }

  async suggestions ({
    params,
    hOtelContext,
  }: {
    params: FollowUpsParams<C>,
    hOtelContext: HOtelContext,
  }): Promise<Suggestion[]> {
    const job = await this.submitChat({
      query: `follow-up: ${JSON.stringify(params.keywords.map(k => k.toLowerCase()))}`,
      conversationId: params.conversationId,
      context: params.context,
      hOtelContext,
    });
    return new Promise<Suggestion[]>((resolve, reject) => {
      Jobs.pollResults({ job_id: job.id, status: job.status }, {
        getResultsFunc: Jobs.getResults,
        errorFunc: reject,
        failureFunc: reject,
        cancelFunc: () => reject(new Error('The job has been cancelled')),
        successFunc: (response: any) => {
          const { data } = response;
          resolve(handleSugaAnswer(data.answer));
        },
      });
    });
  }

  onboardingItems (): OnboardingItem[] {
    return [
      {
        imageUrl: 'https://cdn.holistics.io/assets/ai-query-data-illustration-20250421-758.svg',
        label: 'Query data',
        description: 'Ask questions about your data and make calculations using natural language.',
        enabled: true,
        nudges: [
          {
            icon: 'list-ul',
            label: 'List',
            iconClass: 'text-blue-600',
          },
          {
            icon: 'comparison',
            label: 'Compare',
            iconClass: 'text-blue-600',
          },
          {
            icon: 'custom-chart/combination',
            label: 'Show',
            iconClass: 'text-blue-600',
          },
          {
            icon: 'calculator',
            label: 'Calculate',
            iconClass: 'text-blue-600',
          },
          {
            icon: 'type/aggregated',
            label: 'Count',
            iconClass: 'text-blue-600',
          },
          {
            icon: 'type/aggregated',
            label: 'Sum',
            iconClass: 'text-blue-600',
          },
          {
            icon: 'type/aggregated',
            label: 'Average',
            iconClass: 'text-blue-600',
          },
        ],
      },
      {
        imageUrl: 'https://cdn.holistics.io/assets/ai-customize-chart-illustration-20250421-760.svg',
        description: 'Quickly adjust your charts using simple commands.',
        label: 'Customize chart',
        enabled: false,
      },
      {
        imageUrl: 'https://cdn.holistics.io/assets/ai-explain-chart-illustration-20250421-759.svg',
        label: 'Explain chart',
        description: 'Gain insights from your charts with clear explanations of trends and patterns.',
        enabled: false,
      },
    ];
  }

  async submitChat ({
    query,
    conversationId,
    context,
    hOtelContext,
  }: {
    query: string,
    conversationId: string,
    context: C,
    hOtelContext: HOtelContext,
  }) {
    // const answer = `
    //   \`\`\`aql
    //   metric report_count = count(public_query_reports.id);

    //   explore {
    //     dimensions {
    //       user_name: public_users.name,
    //       tenant_name: public_tenants.name,
    //     }
    //     measures {
    //       user_report_count: report_count,
    //       tenant_report_count: exclude_grains(report_count, public_users.id),
    //       user_report_percentage: (report_count * 100.0) / exclude_grains(report_count, public_users.id),
    //     }
    //   }
    //   \`\`\`
    // `;
    // const answer = `
    //   \`\`\`aql
    //   metric user_count = count(public_users.id);

    //   explore {
    //     dimensions {
    //       country_name: public_tenants.name,
    //     }
    //     measures {
    //       user_count: user_count,
    //       rank_by_user_count: rank(order: user_count | desc())
    //     }
    //     filters {
    //       rank_by_user_count not in [1,2,3]
    //     }
    //   }
    //   \`\`\`
    // `;
    // handleCompletedAnswer({ context, answer });

    // throw new Error('asd')
    let { datasetAmlBinding } = context;
    if (typeof datasetAmlBinding === 'function') {
      datasetAmlBinding = await datasetAmlBinding();
    }
    this.answerTicks = 0;
    return submitChat({
      conversation_id: conversationId,
      query,
      agent: {
        _name: this.name,
        dataset_id: context.datasetId,
        dataset_aml_binding: datasetAmlBinding,
        project_id: context.projectId,
        viz_setting: convertSettingForBackend(cleanUpSetting(context.vizSetting)),
      },
    }, { hOtelContext });
  }

  handleAnswer ({
    context,
    answer,
    completed,
    hOtelContext,
  }: {
    context: C,
    answer: string,
    completed: boolean,
    hOtelContext: HOtelContext,
  }) {
    if (!answer.match(/^\s*#/)) return { raw: answer, message: answer };

    if (this.answerTicks === undefined) this.answerTicks = 0;
    this.answerTicks += 1;

    return {
      raw: answer,
      thoughts: answer,
      message: '',
    };
  }

  async actOnAnswer ({
    context,
    answer,
    hOtelContext,
    updateAnswer,
  }: {
    context: C,
    answer: Answer,
    hOtelContext: HOtelContext,
    updateAnswer: (answer: Answer) => void,
  }) {
    if (!answer.raw.match(/```aql/)) return;

    let summary = '';
    const thoughts = answer.raw.replace(/### Summary([\s\S]*)$/i, (...match) => {
      summary = match[1].trim();
      return '';
    });

    let result: Awaited<ReturnType<typeof buildVizSetting>>;
    try {
      if (answer.artifact?.vizSetting) {
        result = {
          vizSetting: answer.artifact.vizSetting,
          diagnostics: [],
        };
      } else {
        result = await withinSpan(
          'Aqua#buildVizSetting',
          {
            parentSpan: hOtelContext.activeSpan,
          },
          (activeSpan) => buildVizSetting({
            current: context.vizSetting,
            dataset: context.dataset,
            answer: answer.raw,
            summary,
            hOtelContext: { activeSpan },
          }),
        );
      }
    } catch (error: any) {
      console.debug(error);
      hOtelContext.activeSpan?.setError(error);
      updateAnswer({
        ...answer,
        message: 'Sorry, I seem to have made a mistake when writing the AQL. Please retry.',
        thoughts: `${thoughts}\n\n### Error\n${(error as Error).message}`,
        error: true,
        diagnostics: [`AQL syntax error: ${(error as Error).message}`],
      });
      return;
    }

    console.debug(result);
    // TODO: try again button
    if (result?.vizSetting) {
      updateAnswer({
        ...answer,
        thoughts,
        message: 'Generating new chart...',
        artifact: {
          type: 'chartArtifact',
          name: summary,
          vizSetting: result.vizSetting,
        },
      });
      try {
        eventBus.$emit(GlobalEvents.vizSettingForm_onVizSettingChanged, { vizSetting: result.vizSetting, shouldEmitChange: true });
        await nextTick(); // (naively) wait for viz setting form event cycle
        await context.forceRender({
          hOtelContext,
        });
      } catch (error: any) {
        console.debug(error);
        hOtelContext.activeSpan?.recordError(error);
      }
      if (result.diagnostics.length) {
        // TODO: narrow down retriable errors
        updateAnswer({
          ...answer,
          artifact: {
            type: 'chartArtifact',
            name: summary,
            vizSetting: result.vizSetting,
          },
          message: `
I have generated a new chart, but sorry about the errors. You can retry or fix errors on Visualization settings.
          `.trim(),
          thoughts: `${thoughts}\n\n### Error\n${result.diagnostics.map(d => `\n* ${d.message}`)}`,
          diagnostics: result.diagnostics.map(d => `AQL semantic error: ${d.message}`),
        });
      } else {
        updateAnswer({
          ...answer,
          thoughts,
          message: 'New chart has been generated.',
          artifact: {
            type: 'chartArtifact',
            name: summary,
            vizSetting: result.vizSetting,
          },
        });
      }
    } else {
      updateAnswer({
        ...answer,
        message: 'Sorry, I faced some errors when building the chart. Please retry.',
        error: true,
      });
    }
  }

  checkRestorable (context: C, answer: Answer): RestorableStatus {
    if (!answer.artifact) return 'non-restorable';

    const areEqual = areVizSettingsEqual(answer.artifact.vizSetting, context.vizSetting, {
      customFilter (diffs: Operation[]) {
        return diffs
          // ignore changes on _alias and full_path of fields
          .filter(d => !d.path.match(/^\/fields\/.*\/(_alias|full_path)$/))
          // ignore these attributes of adhoc_fields because they are only normalized values, they actually cannot be changed
          .filter(d => !(d.op === 'add' && d.path.match(/^\/adhoc_fields\/\d+\/(is_external|is_hidden|model|joins_path|order)$/)))
          .filter(d => !(d.op === 'add' && d.path.match(/^\/amql\/adhoc_fields\/\d+\/field\/(is_hidden|order|is_literal|_external_field)$/)))
          // ignore additions of series_hash values
          .filter(d => !(d.op === 'add' && d.path.match(/^\/fields\/y_axes\/\d+\/columns\/\d+\/series_settings\/series_hash\//)))
          // ignore additions of null attributes
          .filter(d => !(d.op === 'add' && d.value === null));
      },
    });
    return areEqual ? 'current' : 'restorable';
  }
}

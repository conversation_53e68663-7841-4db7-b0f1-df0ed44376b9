<template>
  <HModal
    v-h-loading.body="isFetching"
    class="data-schedule-modal !z-[1040]"
    :shown="shown"
    size="lg"
    :title="'Data Delivery'"
    :resolve-loading="!isPresetsReady || isFetching"
    prevent-click-outside
    prevent-press-escape
    prevent-close-animation-disabled
    @update:shown="(value: boolean) => emit('update:shown', value)"
  >
    <template #description>
      Automatically push data to your preferred channels.
      <a
        target="_blank"
        href="https://docs.holistics.io/docs/delivery/email-schedules"
      >
        Learn more
      </a>
    </template>
    <div
      v-if="!isFetching"
      class="data-schedule"
    >
      <div class="flex flex-row pb-5">
        <div class="flex-col-3 flex items-center">
          <div class="font-medium">
            Destination
          </div>
        </div>
        <div class="flex-col-9 flex flex-row items-center space-x-2">
          <DestTypePicker
            :model-value="selectedDestType"
            :source="source"
            @update:model-value="updateDestType"
          />
        </div>
      </div>

      <!-- Schedule Title Input -->
      <div class="flex flex-row pb-5">
        <div class="flex-col-3 flex items-center">
          <div class="font-medium">
            Schedule title
          </div>
        </div>
        <div class="flex-col-9">
          <input
            v-model="mutableDataSchedule.title"
            class="h-input h-form-input"
            type="text"
            placeholder="A short description of this schedule"
          >
        </div>
      </div>

      <hr class="-mx-1 my-1 border-gray-300">
      <FilterPresetsForm
        ref="filter-presets-form"
        v-model="mutableDataSchedule.dynamic_filter_presets"
        presettable-type="EmailSchedule"
        :presettable-id="mutableDataSchedule.id || null"
        :filter-holdable-type="source.sourceType"
        :filter-holdable-id="source.data.id"
        :dashboard-filter-conditions="dashboardFilterConditions"
        @ready="() => isPresetsReady = true"
      />
      <div v-if="mutableDataSchedule.dest">
        <component
          :is="destFormComponent"
          ref="dest-form"
          v-model="mutableDataSchedule.dest"
          :data-schedule="mutableDataSchedule"
          :metadata="metadata"
          @dismiss="handleDestTypeChangeFailure"
        >
          <template
            #send-test-trigger
          >
            <DestFormSendTest
              class="mt-2"
              :dest="mutableDataSchedule.dest"
              :can-perform-send-test="isPresetsReady && !isTestJobActive"
              :testing-status="testingStatus"
              @send-test="sendTest"
              @show-test-job-log="showTestJobLog"
            />
          </template>
          <template #schedule>
            <hr class="-mx-1 my-1 border-gray-300">
            <div class="flex flex-row py-5">
              <div class="flex-col-3">
                <label class="pt-2 font-medium">Frequency</label>
              </div>
              <div class="flex-col-9">
                <ScheduleSelector
                  :schedule="mutableDataSchedule.schedule"
                  :support-send-once="!isEditing"
                  @handle="(schedule) => mutableDataSchedule.schedule = schedule"
                />
              </div>
            </div>
          </template>
          <template
            v-if="isTabbedDashboard"
            #attachments-note
          >
            <div
              class="mb-5 flex"
            >
              <div class="flex-col-3" />
              <div class="flex-col-9">
                <HAlert
                  type="info"
                  title="Dashboard Tabs (Beta)"
                >
                  Only the content from the first tab is included in the exported PNG/PDF files.
                </HAlert>
              </div>
            </div>
          </template>
        </component>
      </div>
    </div>
    <template #footer>
      <div class="flex justify-between p-4">
        <div
          class="flex items-center"
        >
          <div
            v-if="isSendOnce && testingStatus"
            class="test-status ci-test-status cursor-pointer"
            @click="showTestJobLog"
          >
            <JobStatus :status="testingStatus" />
          </div>
        </div>
        <div>
          <HButton
            type="secondary-default"
            class="ci-close-modal"
            @click="emit('dismiss')"
          >
            Cancel
          </HButton>
          <HButton
            type="primary-highlight"
            class="ci-submit-btn ml-1"
            :disabled="!isPresetsReady || isSubmitting || (isSendOnce && isTestJobActive)"
            @click.prevent="onSubmit"
          >
            <h-icon
              v-if="isSubmitting || (isSendOnce && isTestJobActive)"
              name="circle-notch"
              class="align-top"
              spin
            />
            {{ resolveBtnText }}
          </HButton>
        </div>
      </div>
    </template>
  </HModal>
  <HModal
    v-if="testJobId"
    v-model:shown="jobLogsModalShown"
    :title="`Logs for job #${testJobId}`"
    size="lg"
    no-footer
  >
    <JobLogs
      :job-id="testJobId"
    />
  </HModal>
</template>
<script setup lang="ts">
import {
  ref, watch, computed,
} from 'vue';
import {
  HModal, HButton,
  SegmentedControlItem,
  HAlert,
} from '@holistics/design-system';
import { DEST_LIST, DestOption } from '@/modules/DataSchedule/constants/dests';
import {
  getDataSchedule, executeDataSchedule, updateDataSchedule, createDataSchedule,
} from '@/modules/DataDelivery/services/dataSchedules.ajax';
import FilterPresetsForm from '@/modules/DynamicFilterPresettable/components/ui/FilterPresetsForm.vue';
import EmailDestForm from '@/modules/DataSchedule/components/forms/EmailDestForm.vue';
import GsheetDestForm from '@/modules/DataSchedule/components/forms/GoogleSheetDestForm.vue';
import SftpDestForm from '@/modules/DataSchedule/components/forms/SftpDestForm.vue';
import type Condition from '@/modules/Viz/submodules/VizFilters/models/Condition';
import DestFormSendTest from '@/modules/DataDelivery/components/DestFormSendTest.vue';
import ScheduleSelector from '@/modules/DataSchedule/components/ScheduleSelector.vue';
import { isActiveJob } from '@/modules/Jobs/helpers/checkStatus';
import Jobs from '@/jobs/jobs';
import { Job } from '@/modules/Jobs/types/job';
import { handleAjaxError } from '@/core/services/ajax';
import Schedule from '@/es6/schedule';
import DestFormSlack from '@/modules/DataDelivery/components/DestFormSlack.vue';
import JobLogs from '@/modules/Jobs/components/JobLogs.vue';
import { success } from '@/core/services/notifier';
import JobStatus from '@/vue_components/job_status.vue';
import { buildDashboardVizConditionsDisplayText } from '@/modules/DataAlerts/services/buildDataAlertPreviewMessage';
import { buildDateVariables } from '@/modules/DataSchedule/services/dynamicVariables/buildDateVariables';
import DestTypePicker from '@/modules/DataDelivery/components/DestTypePicker.vue';
import {
  DataDeliverySource, DataSchedule, DestType, InitialDataSchedule,
} from '../types';

const props = withDefaults(defineProps<{
  shown: boolean,
  dataSchedule?: InitialDataSchedule | DataSchedule,
  source: DataDeliverySource,
  dashboardFilterConditions: Record<string, Condition>
  isOnce: boolean
}>(), {
  dataSchedule: () => ({
    dest: { type: 'EmailDest' },
  }) as InitialDataSchedule,
  isOnce: false,
});

const emit = defineEmits<{(e: 'update:shown', value: boolean): void,
  (e: 'dismiss'): void,
  (e: 'resolve', dataSchedule: DataSchedule): void,
}>();

const mutableDataSchedule = ref<any>({});

const selectedDestType = ref<DestType>(DEST_LIST[0].type as unknown as DestType);
const previousDestType = ref(selectedDestType.value);

const isEditing = computed(() => {
  return !!props.dataSchedule.id;
});

const isFetching = ref(false);
const isPresetsReady = ref(false);
const isSubmitting = ref(false);

const destFormComponent = computed(() => {
  if (selectedDestType.value === 'SftpDest') {
    return SftpDestForm;
  }
  if (selectedDestType.value === 'SlackDest') {
    return DestFormSlack;
  }
  if (selectedDestType.value === 'GsheetDest') {
    return GsheetDestForm;
  }
  return EmailDestForm;
});

const isSendOnce = computed(() => {
  return mutableDataSchedule.value?.schedule?.isSendOnce;
});

const resolveBtnText = computed(() => {
  if (isSendOnce.value) return 'Send now';
  return isEditing.value ? 'Update' : 'Create';
});

const testingStatus = ref<string>('');
const isTestJobActive = computed(() => {
  if (!testingStatus.value) {
    return false;
  }
  return isActiveJob(testingStatus.value);
});
const testJobId = ref<null | number>(null);

async function prepareDataSchedule () {
  isFetching.value = true;
  selectedDestType.value = props.dataSchedule.dest.type;
  if (props.dataSchedule.id) {
    const res = await getDataSchedule(props.dataSchedule.id);
    mutableDataSchedule.value = res.data_schedule;
  } else {
    mutableDataSchedule.value.source_type = props.source.sourceType;
    mutableDataSchedule.value.source_id = props.source.data.id;
    mutableDataSchedule.value.dest = { type: selectedDestType.value };
    mutableDataSchedule.value.dynamic_filter_presets = [];
    mutableDataSchedule.value.schedule = Schedule.new();
    mutableDataSchedule.value.schedule.isSendOnce = props.isOnce;
  }
  mutableDataSchedule.value.source = props.source.data;
  isFetching.value = false;
}

watch(() => props.dataSchedule, (value) => {
  if (value) {
    prepareDataSchedule();
  }
}, { immediate: true, deep: true });

function checkDestEnabled (destOption: DestOption) {
  return destOption.checkEnabled({ sourceType: props.source.sourceType, version: '4' });
}
function checkDestComingSoon (destOption: DestOption) {
  return destOption.checkComingSoon({ sourceType: props.source.sourceType, version: '4' });
}

function updateDestType (newDestType: string) {
  previousDestType.value = selectedDestType.value;
  selectedDestType.value = newDestType;
}

function handleDestTypeChangeFailure () {
  selectedDestType.value = previousDestType.value;
}

const availableDestOptions = DEST_LIST.filter((destOption) => {
  // if a dest is neither enabled nor coming soon, it will be hidden
  // if a dest is not enabled but coming soon, it will be displayed as disabled
  return checkDestEnabled(destOption) || checkDestComingSoon(destOption);
});

function canChangeToDest (destOption: DestOption) {
  return !isEditing.value && checkDestEnabled(destOption);
}

function tooltipForChangingDest (destOption: DestOption) {
  if (destOption.type === mutableDataSchedule.value?.dest?.type) return null;
  if (isEditing.value) return 'You can\'t change the destination type, please create a new schedule';
  if (!checkDestEnabled(destOption)) return 'Not supported yet';

  return null;
}

async function pollTestJob (job: Job) {
  await Jobs.simplePollResults({ ...job, job_id: job.id }, {
    statusFunc: ({ status }) => { testingStatus.value = status; },
  });
}

async function sendTest () {
  if (!isPresetsReady.value) {
    return;
  }
  if (isActiveJob(testingStatus.value)) {
    return;
  }
  try {
    testingStatus.value = 'submitting';
    const { job } = await executeDataSchedule({ testDataSchedule: mutableDataSchedule.value });
    testJobId.value = job.id;
    pollTestJob(job);
  } catch (error) {
    testingStatus.value = 'failure';
    handleAjaxError(error);
  }
}

const jobLogsModalShown = ref(false);

function showTestJobLog () {
  if (testJobId.value) {
    jobLogsModalShown.value = true;
  }
}

async function onCreateOrUpdate () {
  isSubmitting.value = true;
  let dataSchedule;
  try {
    if (isEditing.value) {
      ({ data_schedule: dataSchedule } = await updateDataSchedule(mutableDataSchedule.value));
    } else {
      ({ data_schedule: dataSchedule } = await createDataSchedule(mutableDataSchedule.value));
    }
    success(`Data Schedule has been ${isEditing.value ? 'updated' : 'created'} successfully!`);
    emit('resolve', dataSchedule);
  } catch (error) {
    handleAjaxError(error);
  } finally {
    isSubmitting.value = false;
  }
}

async function onSubmit () {
  if (isSendOnce.value) {
    await sendTest();
  } else {
    await onCreateOrUpdate();
  }
}

watch(() => selectedDestType.value, () => {
  testingStatus.value = '';
  if (selectedDestType.value) {
    mutableDataSchedule.value.dest_type = selectedDestType.value;
  }
});

const destOptions = computed(() => {
  return availableDestOptions.map(destOption => ({
    label: destOption.displayName,
    value: destOption.type,
    icon: destOption.icon,
    disabled: !canChangeToDest(destOption),
    tooltip: tooltipForChangingDest(destOption) ?? undefined,
    class: `ci-${destOption.kebabType}`,
  })) as SegmentedControlItem<string>[];
});

const metadata = computed(() => {
  const dashboardPath = new URL(mutableDataSchedule.value.source.path, window.location.origin).toString();
  return {
    dashboard_title: mutableDataSchedule.value.source.title,
    dashboard_url: dashboardPath,
    dashboard_controls: buildDashboardVizConditionsDisplayText(mutableDataSchedule.value.dynamic_filter_presets, false, selectedDestType.value),
    ...buildDateVariables(),
  };
});

const isTabbedDashboard = computed(() => {
  return props.source.data.definition?.views[0]?.type === 'TabLayout';
});
</script>

@import '../variables';

$big-screen-cell-height: 28px;
$small-screen-cell-height: 24px;
$absolute-big-screen-cell-height: $big-screen-cell-height - 1px;
$absolute-small-screen-cell-height: $small-screen-cell-height - 1px;

$default-border-color: theme('colors.gray.400');
$default-h-total-font-weight: 600;
$default-horizontal-padding: 4px;
$default-vertical-padding: 5px;
$default-table-line-height-ratio: 1.5;

// pivot table styling variables
// pivot value headers
$default-header-bg-color: theme('colors.gray.300');
$default-sub-header-bg-color: theme('colors.gray.100');

.h-table-header-dropdown {
  .selected-sort-option {
    @apply text-blue-500 font-medium;
  }
}

.ag-standard {
  --ag-header-column-border-height: 100%;
  --ag-cell-horizontal-padding: var(--ag-spacing);
  --ag-header-column-resize-handle-width: 0px;
  --ag-wrapper-border-radius: 3px;
  --ag-row-height: calc(var(--ag-font-size) * $default-table-line-height-ratio + var(var(--h-table-cell-padding-bottom, $default-vertical-padding) + var(--h-table-cell-padding-top, $default-vertical-padding)) - var(--h-table-border-width, 1px));
  --ag-line-height: calc(var(--ag-font-size) * $default-table-line-height-ratio - var(--h-table-border-width, 1px));

  .ag-root-wrapper {
    // override the AG-Grid border style
    border-width: var(--h-table-wrapper-border-width, var(--h-table-border-width, 1px));

    background-color: theme('colors.white');

    // force ag-header fit height of ag-header-cell
    .ag-header {
      @apply w-fit max-w-full;
      background: var(--ag-header-background-color, theme('colors.gray.100'));
    }
    .ag-center-cols-container,
    .ag-pinned-left-cols-container {
      background: var(--ag-background-color, theme('colors.white'));
    }

    /* Spacing style */
    .ag-cell,
    .ag-header-cell,
    .ag-header-group-cell {
      padding-left: var(--h-table-cell-padding-left, $default-horizontal-padding);
      padding-right: var(--h-table-cell-padding-right, $default-horizontal-padding);
      line-height: calc(var(--ag-font-size) * $default-table-line-height-ratio);
    }
    // Component-specific padding
    .ag-cell,
    .ag-header-cell {
      padding-top: calc(var(--h-table-cell-padding-top, $default-vertical-padding) - var(--h-table-border-width, 1px));
      padding-bottom: calc(var(--h-table-cell-padding-bottom, $default-vertical-padding) - var(--h-table-border-width, 1px));
    }
    .ag-header-group-cell .ag-header-cell-comp-wrapper,
    // AG Grid automatically calculates row height for header group cells in wrapper instead of cell
    .ag-floating-bottom .ag-cell {
      // 1px comes from ag-grid
      padding-top: calc(var(--h-table-padding-top, $default-vertical-padding) - var(--h-table-border-width, 1px));
      padding-bottom: calc(var(--h-table-padding-bottom, $default-vertical-padding) - var(--h-table-border-width, 1px) - 1px);
    }

    .ag-row {
      &.ag-row-odd, &.ag-row-even {
        @apply border-b-0;
      }
    }
  }

  .col-header-wrapper {
    @apply flex h-full w-full flex-col items-center;

    .reorder-indicator {
      @apply absolute -top-1.5;
    }

    .col-header {
      @apply flex items-center w-full h-full;
    }
  }

  // Freeze column style
  .ag-pinned-left-header,
  .ag-body-horizontal-scroll:not(.ag-scrollbar-invisible) .ag-horizontal-left-spacer:not(.ag-scroller-corner) {
    border-right: var(--ag-pinned-column-border);
  }

  .ag-pinned-left-cols-container,
  .ag-pinned-left-floating-top,
  .ag-pinned-left-floating-bottom {
    // Override the AG-Grid right range
    :not(.ag-cell-range-right) {
      .ag-cell.ag-cell-last-left-pinned {
        @apply focus:border-r-blue-400;
        border-right: var(--ag-pinned-column-border);

        &.h-range-selection-cell {
          @apply border-r-blue-700;
        }
      }
    }
  }

  .ag-floating-top {
    @apply border-b-0;

    .ag-row:last-child {
      border-bottom: var(--ag-pinned-row-border);
    }
  }

  .ag-floating-bottom {
    @apply border-t-0;

    // bottom total average rows container
    .ag-floating-bottom-container, .ag-pinned-left-floating-bottom {
      border-top: var(--ag-pinned-row-border);
    }
  }

  // TODO: remove this when AG-Grid release fix for AG-14241
  // AG-Grid has issues with auto row-height calculation in floating rows. See issue: https://github.com/ag-grid/ag-grid/issues/10057
  // Force text truncate to prevent incorrect row height and text overflow issues.
  // See more in: https://holistics.slack.com/archives/C04K5UHB84B/p1741601057072289
  .ag-floating-top, .ag-floating-bottom {
    .ag-cell {
      @apply truncate;
    }
  }

  .h-loading-cell {
    @apply bg-gray-300 rounded-3xl animate-pulse h-4 mt-0.5;
  }

  .h-number-cell {
    @apply text-right tabular-nums;
  }

  .h-sub-total-title-cell {
    @apply text-left;
  }

  // sub-total row
  .ag-row:has(.h-sub-total-cell){
    background: var(--ag-background-color, theme('colors.white'));
  }

  .h-row-number-cell {
    @apply tabular-nums;
  }

  .h-aggregated-cell {
    @apply font-semibold;
  }

  // row border
  .ag-row:not(.ag-row-last, .ag-row-pinned:last-child)
    .ag-cell:not(
      .h-range-selection-cell,
      .h-last-cell-in-row-group,
      :focus,
      .h-conditional-formatting-border
    ) {
    border-bottom: var(--ag-row-border);
  }

  .ag-row-pinned {
    @apply font-semibold;
    background: var(--ag-background-color, theme('colors.white'));

    // bottom total average rows
    &:not(:last-child) {
      border-bottom: var(--ag-pinned-row-border);
    }
  }

  .h-cross-filter {
    &.h-filterable-element {
      @apply cursor-pointer;
    }

    &.h-active-element {
      @apply opacity-100;
    }

    &.h-inactive-element {
      @apply opacity-65;
    }
  }


  .h-range-selection-cell {
    @apply text-blue-700;
    // Product design: keep cell border for range selection cells
    border-bottom-width: var(--h-table-border-width, 1px);
    border-bottom-color: theme('colors.white');
    border-right-width: var(--h-table-border-width, 1px);

    &.h-background-color {
      @apply bg-blue-50;
    }

    &.h-left-border {
      @apply border-l-blue-700;
    }

    &.h-right-border {
      @apply border-r-blue-700;
    }

    &.h-top-border {
      @apply border-t border-t-blue-700;
    }

    &.h-bottom-border {
      @apply border-b-blue-700;
    }

    &.h-row-number-cell {
      @apply bg-blue-50;
      &.h-row-number-dragging {
        @apply bg-blue-50 text-blue-700;
      }
    }
  }
}

.pivot-table-wrapper, .data-table-wrapper {
  &.hide-table {
    .handsontable, .table-pagination .table-header {
      visibility: hidden;
    }
  }

  td {
    white-space: pre-wrap;
    word-break: break-word;
  }

  thead {
    td, th {
      height: $absolute-big-screen-cell-height !important;
    }
  }
  @media only screen and (max-width: 1440px) {
    thead {
      td, th {
        height: $absolute-small-screen-cell-height !important;
      }
    }
  }

  .single-line-row {
    td, th, .empty-cell {
      height: $absolute-big-screen-cell-height !important;
    }
    td, td span.cell-wrapper span.inner {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    td span.cell-wrapper span.inner  {
      max-width: calc(100% - 20px);
    }

    .conditional-format {
      // conditional format border make cell 1px higher. need to -1px to make it equal other cells
      height: $absolute-big-screen-cell-height - 1px !important;
    }
    @media only screen and (max-width: 1440px) {
      td, th, .empty-cell {
        height: $absolute-small-screen-cell-height !important;
      }
      .conditional-format {
        height: $absolute-small-screen-cell-height - 1px !important;
      }
    }
  }

  .has-highlighted-cells {
    td:not(.highlighted-cell) {
      opacity: 0.2;
    }
    .highlighted-cell:not(.remove-top) {
      box-shadow: 0 -1px 0 0 #DEDEDE; // fake border-top
    }
  }

  .has-cross-filter {
    .ht_master, .ht_clone_left {
      td {
        cursor: pointer;
      }
    }
  }

  .conditional-format {
    border: 1px solid #DEDEDE;
  }

  // probably handsontable bugs, clone bottom is shown even there is no fixed row bottom
  &.hidden-clone-bottom-table {
    .ht_clone_bottom.handsontable {
      visibility: hidden;
    }
  }

  .h-table {
    @apply ag-standard;

    /* The last column should use table border color instead of grid border color*/
    .ag-cell.ag-column-last {
      // override the AG-Grid border style
      border-right-color: var(--ag-border-color, $default-border-color);
      border-right-width: var(--h-table-wrapper-border-width, var(--h-table-border-width, 1px));
    }
    // Note: AG-Grid adds header border styling using pseudo-elements
    // instead of the actual border property
    .ag-header-cell.ag-column-last::after,
    .ag-header-group-cell.ag-column-last:not(.ag-header-span-height.ag-header-group-cell-no-group)::after {
      --ag-header-column-border: var(--h-table-wrapper-border-width, var(--h-table-border-width, 1px)) solid var(--ag-border-color, $default-border-color);
    }

    .col-header {
      .table-normal-header {
        @apply justify-between;
      }

      .table-number-header {
        @apply justify-end;
      }
    }

    .ag-header {
      .ag-header-cell {
        min-height: calc(var(--ag-row-height) + var(--h-table-border-width, 1px)) !important;
        // AG-Grid uses pseudo-elements to add styling for dragging/moving headers
        &.ag-header-cell-moving::before {
          background: var(--ag-background-color, theme('colors.white'));
        }
      }
    }

      // Add new column
      [col-id="add-new-column"] {
        &.ag-header-cell {
          background: var(--ag-background-color, theme('colors.white'));
          --ag-header-cell-hover-background-color: var(--ag-background-color, theme('colors.white'));

          // Adjust icon size in custom header to match ag-grid's icon sizing
          .h-icon svg {
            --h-icon-ratio: calc(4/3);
            width: calc(var(--ag-header-font-size, var(--ag-font-size)) * var(--h-icon-ratio)) !important;
            height: calc(var(--ag-header-font-size, var(--ag-font-size)) * var(--h-icon-ratio)) !important;
          }
        }
      }
    }
  }

  .h-pivot {
    @apply ag-standard;

    --ag-pinned-column-border: var(--ag-pinned-column-border, 3px) solid var(--h-table-border-color, theme('colors.gray.400'));
    .ag-pinned-left-cols-container,
    .ag-pinned-left-floating-top,
    .ag-pinned-left-floating-bottom {
      :not(.ag-cell-range-right) {
        .ag-cell:last-child {
          @apply focus:border-r-blue-400;
          border-right: var(--ag-pinned-column-border);
        }
      }
    }

    .col-header {
      .pivot-column-fields-and-row-fields-header {
        @apply justify-start;
      }

      .pivot-column-values-and-measure-fields-header {
        @apply justify-center;
      }
    }

    /* The last column should use table border color instead of grid border color*/
    .ag-cell.ag-column-last {
      // override the AG-Grid border style
      border-right-width: var(--h-table-wrapper-border-width, var(--h-table-border-width, 1px));
      border-right-color: var(--ag-border-color, $default-border-color);
    }
    // AG-Grid adds header border styling using pseudo-elements
    .ag-header-cell.ag-column-last::after,
    .ag-header-group-cell.ag-column-last:not(.ag-header-span-height.ag-header-group-cell-no-group)::after {
      --ag-header-column-border: var(--h-table-wrapper-border-width, var(--h-table-border-width, 1px)) solid var(--ag-border-color, $default-border-color);
    }

    .ag-header-row:not(:first-child) .ag-header-cell:not(.ag-header-span-height.ag-header-span-total, .ag-header-parent-hidden),
    .ag-header-row:not(:first-child) .ag-header-group-cell.ag-header-group-cell-with-group {
      border-top: 0;
    }

    .ag-header {
      .ag-header-cell {
        min-height: calc(var(--ag-row-height) + var(--h-table-border-width, 1px)) !important;

        &:after {
          border-right: var(--ag-header-column-border);
        }
      }

      // pivot value header
      .h-pivot-value-header-cell {
        background: var(--h-table-header-bg-color, var(--h-table-fallback-header-bg-color, $default-header-bg-color));
        font-size: var(--ag-header-font-size, var(--ag-font-size));
        font-weight: var(--ag-header-font-weight);
        color: var(--ag-header-text-color, var(--ag-text-color));
      }

      // pivot row header
      .h-pivot-row-header-cell {
        background: var(--h-specific-table-header-bg-color,
              var(--h-specific-table-row-header-bg-color,
                var(--h-specific-fallback-table-header-bg-color,
                  var(--h-dashboard-table-header-bg-color,
                    var(--h-dashboard-table-row-header-bg-color,
                      var(--h-dashboard-fallback-table-header-bg-color,
                        $default-header-bg-color ))))));
          font-size: var(--h-specific-table-header-font-size,
              var(--h-specific-table-row-header-font-size,
                var(--h-specific-fallback-table-header-font-size,
                  var(--h-dashboard-table-header-font-size,
                    var(--h-dashboard-table-row-header-font-size,
                      var(--h-dashboard-fallback-table-header-font-size,
                        var(--ag-header-font-size, var(--ag-font-size))))))));
          font-weight: var(--h-specific-table-header-font-weight,
              var(--h-specific-table-row-header-font-weight,
                var(--h-specific-fallback-table-header-font-weight,
                  var(--h-dashboard-table-header-font-weight,
                    var(--h-dashboard-table-row-header-font-weight,
                      var(--h-dashboard-fallback-table-header-font-weight,
                        var(--ag-header-font-weight, theme('fontWeight.semibold'))))))));
          color: var(--h-specific-table-header-font-color,
              var(--h-specific-table-row-header-font-color,
                var(--h-specific-fallback-table-header-font-color,
                  var(--h-dashboard-table-header-font-color,
                    var(--h-dashboard-table-row-header-font-color,
                      var(--h-dashboard-fallback-table-header-font-color,
                        var(--ag-header-text-color, var(--ag-text-color))))))));

        &::after {
          border-right-width: var(
            --h-table-row-header-border-width,
            var(
              --h-table-column-border-width,
              var(--h-table-border-width, 1px)
            )
          );
        }
      }

      .ag-header-row.ag-header-row-column {
        /* Extra height comes from border-bottom of .ag-header */
        min-height: calc(var(--ag-row-height) + var(--h-table-border-width, 1px)) !important;
      }
    }

    // Total title merged cell
    .h-header-total-title-cell:not(
      .h-range-selection-cell,
      :focus
    ) {
      background: var(--ag-background-color, theme('colors.white')) !important;
    }

    // pivot row header
    .ag-row:not(.ag-row-last, .ag-row-pinned:last-child)
    .ag-cell:not(
      .h-range-selection-cell,
      .h-last-cell-in-row-group,
      :focus,
      .h-conditional-formatting-border
    ) {
      border-bottom-color: var(--ag-background-color, theme('colors.white'));

      &.h-pivot-row-header-cell {
        border-bottom-color: var(--h-table-sub-header-bg-color, var(--h-table-row-header-bg-color, var(--h-table-fallback-sub-header-bg-color, $default-sub-header-bg-color)));
        border-bottom-color: var(--h-specific-table-sub-header-bg-color,
              var(--h-specific-table-row-header-bg-color,
                var(--h-specific-fallback-table-sub-header-bg-color,
                  var(--h-dashboard-table-sub-header-bg-color,
                    var(--h-dashboard-table-row-header-bg-color,
                      var(--h-dashboard-fallback-table-sub-header-bg-color,
                        $default-sub-header-bg-color ))))));
      }
    }
    .ag-row .h-pivot-row-header-cell {
      --ag-row-hover-color: var(
        --h-table-row-header-hover-color,
        var(--h-table-hover-color, #EDEDED)
      );
        font-size: var(--h-specific-table-sub-header-font-size,
              var(--h-specific-table-row-header-font-size,
                var(--h-specific-fallback-table-sub-header-font-size,
                  var(--h-dashboard-table-sub-header-font-size,
                    var(--h-dashboard-table-row-header-font-size,
                      var(--h-dashboard-fallback-table-sub-header-font-size,
                        var(--ag-font-size)))))));
        font-weight: var(--h-specific-table-sub-header-font-weight,
            var(--h-specific-table-row-header-font-weight,
              var(--h-specific-fallback-table-sub-header-font-weight,
                var(--h-dashboard-table-sub-header-font-weight,
                  var(--h-dashboard-table-row-header-font-weight,
                    var(--h-dashboard-fallback-sub-table-header-font-weight,
                      theme('fontWeight.semibold')))))));

      border-right-width: var(
        --h-table-row-header-border-width,
        var(
          --h-table-column-border-width,
          var(--h-table-border-width, 1px)
        )
      );

      &:not(.h-range-selection-cell) {
        color: var(--h-specific-table-sub-header-font-color,
            var(--h-specific-table-row-header-font-color,
              var(--h-specific-fallback-table-sub-header-font-color,
                var(--h-dashboard-table-sub-header-font-color,
                  var(--h-dashboard-table-row-header-font-color,
                    var(--h-dashboard-fallback-table-sub-header-font-color,
                      var(--ag-header-text-color, var(--ag-text-color))))))));
      }
      // default font-weight for sub-total is semi-bold
      &.h-sub-total-cell {
        font-weight: var(--h-table-row-header-font-weight, $default-h-total-font-weight);
      }
    }
    .ag-row-even .h-pivot-row-header-cell:not(.h-range-selection-cell),
    .ag-row-odd .h-pivot-row-header-cell.h-aggregated-cell:not(.h-range-selection-cell) {
      background: var(--h-specific-table-sub-header-bg-color,
            var(--h-specific-table-row-header-bg-color,
              var(--h-specific-fallback-table-sub-header-bg-color,
                var(--h-dashboard-table-sub-header-bg-color,
                  var(--h-dashboard-table-row-header-bg-color,
                    var(--h-dashboard-fallback-table-sub-header-bg-color,
                      $default-sub-header-bg-color ))))));
    }
    .ag-row-odd .h-pivot-row-header-cell:not(.h-range-selection-cell ) {
      background: var(--h-table-sub-header-bg-color, var(--h-table-fallback-sub-header-bg-color, var(--ag-odd-row-background-color, $default-sub-header-bg-color)));
    }

    // sub-total row
    .ag-row:has(.h-sub-total-cell) .h-pivot-row-header-cell:not(.h-range-selection-cell) {
      @apply bg-white;
      background: var(--h-table-sub-header-bg-color, var(--h-table-row-header-bg-color, var(--h-table-fallback-sub-header-bg-color, $default-sub-header-bg-color)));
    }

    .ag-cell {
      border-right-width: var(--h-table-column-border-width, var(--h-table-border-width, 1px));
    }

    // pivot special row
    .ag-floating-bottom {
      // Product design: pivot sticky row borders are bolder than table
      .ag-floating-bottom-container, .ag-pinned-left-floating-bottom {
        border-top: var(--ag-pinned-row-border);
      }
    }

    .ag-row-pinned {
      @apply border-b-white focus:border-b-blue-300 #{!important};
    }

    // pivot row header
    :not(.h-last-cell-in-column-group) {
      // Skip floating bottom container to prevent column total cells from inheriting row header styles
      &.h-following-span:not(.ag-floating-bottom-container .h-following-span) {
        @apply text-transparent;
        background: var(--h-specific-table-sub-header-bg-color,
              var(--h-specific-table-row-header-bg-color,
                var(--h-specific-fallback-table-sub-header-bg-color,
                  var(--h-dashboard-table-sub-header-bg-color,
                    var(--h-dashboard-table-row-header-bg-color,
                      var(--h-dashboard-fallback-table-sub-header-bg-color,
                        $default-sub-header-bg-color ))))));

        &.h-range-selection-cell {
          @apply bg-blue-50;
        }

        // multiple lines
        .ag-cell-wrapper {
          @apply hidden;
        }
      }

      // Skip floating bottom container to prevent column total cells from inheriting row header styles
      &.h-start-span:not(.ag-floating-bottom-container .h-start-span) {
        color: var(ag-text-color, theme('colors.gray.800'));
        background: var(--h-specific-table-sub-header-bg-color,
              var(--h-specific-table-row-header-bg-color,
                var(--h-specific-fallback-table-sub-header-bg-color,
                  var(--h-dashboard-table-sub-header-bg-color,
                    var(--h-dashboard-table-row-header-bg-color,
                      var(--h-dashboard-fallback-table-sub-header-bg-color,
                        $default-sub-header-bg-color ))))));

        &.h-range-selection-cell {
          @apply bg-blue-50 text-blue-700;
        }
      }
    }

    // AG-Grid adds header border styling using pseudo-elements
    .ag-header-group-cell:not(.ag-column-last)::after {
      border-right: var(--ag-header-column-border);
      border-right-color: var(--h-table-grid-color, var(--h-table-border-color, $default-border-color));
    }

    .h-last-cell-in-column-group {
      @apply border-r focus:border-r-blue-400;
      border-right: var(--ag-column-border);
    }

    .h-last-cell-in-row-group:not(.ag-row-last .ag-cell, .h-range-selection-cell) {
      @apply focus:border-b-blue-700;
      border-bottom-color: var(--h-table-grid-color, var(--h-table-border-color, $default-border-color));
      border-bottom-width: var(--h-table-row-border-width, var(--h-table-border-width, 1px));

      &.h-conditional-formatting-border {
        @apply focus:border-b-blue-700;
      }
    }

    // Note: AG-Grid shows `total` title for pinned and unpinned area
    // Product Requirement: Show `total` title on the first column if had
    .ag-row .h-header-total-title-cell:not(.ag-column-first) {
      @apply text-transparent;

      &.h-range-selection-cell {
        @apply text-transparent;
      }
    }

    .h-conditional-formatting-border {
      @apply focus:border-b-blue-700;
    }

    // pivot column header
    .h-pivot-column-header-cell {
      --ag-header-cell-hover-background-color: transparent;

      &.h-pivot-column-header-value-cell {
        background: var(--h-specific-table-sub-header-bg-color,
              var(--h-specific-table-col-header-bg-color,
                var(--h-specific-fallback-table-sub-header-bg-color,
                  var(--h-dashboard-table-sub-header-bg-color,
                    var(--h-dashboard-table-col-header-bg-color,
                      var(--h-dashboard-fallback-table-sub-header-bg-color,
                        var(--h-table-fallback-sub-header-bg-color, $default-sub-header-bg-color)))))));
          font-size: var(--h-specific-table-sub-header-font-size,
              var(--h-specific-table-col-header-font-size,
                var(--h-specific-fallback-table-sub-header-font-size,
                  var(--h-dashboard-table-sub-header-font-size,
                    var(--h-dashboard-table-col-header-font-size,
                      var(--h-dashboard-fallback-table-sub-header-font-size,
                        var(--h-table-sub-header-font-size, var(--ag-font-size, 12px))))))));
          font-weight: var(--h-specific-table-sub-header-font-weight,
              var(--h-specific-table-col-header-font-weight,
                var(--h-specific-fallback-table-sub-header-font-weight,
                  var(--h-dashboard-table-sub-header-font-weight,
                    var(--h-dashboard-table-col-header-font-weight,
                      var(--h-dashboard-fallback-table-sub-header-font-weight,
                        var(--h-table-sub-header-font-weight, theme('fontWeight.semibold'))))))));
          color: var(--h-specific-table-sub-header-font-color,
              var(--h-specific-table-col-header-font-color,
                var(--h-specific-fallback-table-sub-header-font-color,
                  var(--h-dashboard-table-sub-header-font-color,
                    var(--h-dashboard-table-col-header-font-color,
                      var(--h-dashboard-fallback-table-sub-header-font-color,
                        var(--h-table-sub-header-font-color, var(--ag-header-text-color, theme('colors.gray.800')))))))));
      }

      &.h-pivot-column-header-field-cell {
        background: var(--h-specific-table-header-bg-color,
              var(--h-specific-table-col-header-bg-color,
                var(--h-specific-fallback-table-header-bg-color,
                  var(--h-dashboard-table-header-bg-color,
                    var(--h-dashboard-table-col-header-bg-color,
                      var(--h-dashboard-fallback-table-header-bg-color,
                        var(--h-table-fallback-header-bg-color, $default-header-bg-color)))))));
          font-size: var(--h-specific-table-header-font-size,
              var(--h-specific-table-col-header-font-size,
                var(--h-specific-fallback-table-header-font-size,
                  var(--h-dashboard-table-header-font-size,
                    var(--h-dashboard-table-col-header-font-size,
                      var(--h-dashboard-fallback-table-header-font-size,
                        var(--h-table-header-font-size, var(--ag-font-size, 12px))))))));
          font-weight: var(--h-specific-table-header-font-weight,
              var(--h-specific-table-col-header-font-weight,
                var(--h-specific-fallback-table-header-font-weight,
                  var(--h-dashboard-table-header-font-weight,
                    var(--h-dashboard-table-col-header-font-weight,
                      var(--h-dashboard-fallback-table-header-font-weight,
                        var(--h-table-header-font-weight, theme('fontWeight.bold'))))))));
          color: var(--h-specific-table-header-font-color,
              var(--h-specific-table-col-header-font-color,
                var(--h-specific-fallback-table-header-font-color,
                  var(--h-dashboard-table-header-font-color,
                    var(--h-dashboard-table-col-header-font-color,
                      var(--h-dashboard-fallback-table-header-font-color,
                        var(--h-table-header-font-color, var(--ag-header-text-color, theme('colors.gray.800')))))))));
      }

      &.h-header-total-column-group, &.h-header-total-column-group-last {
        background: var(--h-specific-table-header-bg-color,
              var(--h-specific-table-col-header-bg-color,
                var(--h-specific-fallback-table-header-bg-color,
                  var(--h-dashboard-table-header-bg-color,
                    var(--h-dashboard-table-col-header-bg-color,
                      var(--h-dashboard-fallback-table-header-bg-color,
                        var(--h-table-fallback-header-bg-color, $default-header-bg-color)))))));
          font-size: var(--h-specific-table-header-font-size,
              var(--h-specific-table-col-header-font-size,
                var(--h-specific-fallback-table-header-font-size,
                  var(--h-dashboard-table-header-font-size,
                    var(--h-dashboard-table-col-header-font-size,
                      var(--h-dashboard-fallback-table-header-font-size,
                        var(--h-table-header-font-size, var(--ag-font-size, 12px))))))));
          font-weight: var(--h-specific-table-header-font-weight,
              var(--h-specific-table-col-header-font-weight,
                var(--h-specific-fallback-table-header-font-weight,
                  var(--h-dashboard-table-header-font-weight,
                    var(--h-dashboard-table-col-header-font-weight,
                      var(--h-dashboard-fallback-table-header-font-weight,
                        var(--h-table-header-font-weight, theme('fontWeight.bold'))))))));
          color: var(--h-specific-table-header-font-color,
              var(--h-specific-table-col-header-font-color,
                var(--h-specific-fallback-table-header-font-color,
                  var(--h-dashboard-table-header-font-color,
                    var(--h-dashboard-table-col-header-font-color,
                      var(--h-dashboard-fallback-table-header-font-color,
                        var(--h-table-header-font-color, var(--ag-header-text-color, theme('colors.gray.800')))))))));
      }
    }

    .ag-header-group-cell-with-group:not(.h-header-total-column-group) {
      @apply border-b;
      border-bottom-color: var(--h-table-header-row-border-color, var(--h-table-grid-color, var(--h-table-border-color, $default-border-color)));
      border-bottom-width: var(--h-table-header-row-border-width, var(--h-table-border-width, 1px));
    }

    .h-header-total-column-group, .h-header-total-column-group-last {
      @apply border-r-0;
    }
  }

  .h-metric-sheet {
    @apply ag-standard;
    --ag-row-border-width: 0px;

    .ag-root {
      background: var(--ag-background-color, theme('colors.white'));
    }

    .ag-header .ag-header-cell {
      background: var(--ag-header-background-color, theme('colors.gray.100'));

      .metric-sheet-title {
        line-height: normal;
      }

      .metric-sheet-sub-title {
        font-size: var(--h-table-subtitle-font-size, var(--ag-font-size, theme('fontSize.2xs')));
        color: var(--h-table-subtitle-font-color, var(--ag-text-color, theme('colors.gray.600')));
        /*Override font-weight 700 default of alpine theme*/
        font-weight: var(--h-table-subtitle-font-weight, theme('fontWeight.normal'));
        line-height: normal;
      }
    }

    /* The last column in metric sheet already have border from the wrapper*/
    .ag-header-cell.ag-column-last::before,
    .ag-header-group-cell.ag-column-last:not(.ag-header-span-height.ag-header-group-cell-no-group)::before {
      width: 0;
    }

    .ag-cell.ag-column-last {
      border-right-width: 0;
    }

    .h-chart-cell {
      @apply pt-2;
    }
  }

  .h-cohort-retention {
    @apply ag-standard;

    .ag-header {
      .ag-header-viewport .ag-header-cell-label {
        // design requirement: header of value format should be center
        @apply justify-center;
      }
    }

    // Product requirements: border for retention heatmap
    // override the ag-standard
    .ag-center-cols-container
      .ag-row:not(.ag-row-last, .ag-row-pinned:last-child)
      .ag-cell:not(
        .h-range-selection-cell,
        .h-last-cell-in-row-group,
        :focus,
        .h-conditional-formatting-border
      ) {
      @apply border-b border-solid border-b-gray-400;
    }
  }

.retention-heatmap, .pivot-table, .data-table {
  &,
  *,
  *:before,
  *:after {
    box-sizing: content-box !important;
  }

  .htDimmed {
    color: $color-dark-2;
  }

  td, th {
    font-size: $font-size-xs;
    line-height: $line-height-xs !important;
    border-color: #DEDEDE !important;
    vertical-align: middle;
  }

  &:first-child tr {
    th {
      border-bottom: 1px solid #DEDEDE !important;
    }
    td {
      border-top: 0;
    }
  }

  .ht_clone_top, .ht_clone_left, .ht_clone_top_left_corner, .ht_clone_bottom_left_corner {
    z-index: 10;
  }

  // infinite scroll's CSS:
  // for loading cell, merge cell
  td {
    div.loading-cell {
      background: rgb(235,235,235);
      height: 13px;
      border-radius: $border-radius-xl;
      animation-name: shine;
      animation-duration: 2s;
      animation-iteration-count: infinite;
      animation-timing-function: linear;
    }
    &.remove-bottom {
      border-bottom-width: 0px;
    }
    &.remove-top {
      border-top-width: 0px;
    }
    &.remove-left {
      border-left-width: 0px;
    }
    &.remove-right {
      border-right-width: 0px;
    }
  }

  // need to set these in both handsontable settings and css due to this bug https://github.com/handsontable/handsontable/issues/4454#issuecomment-519405266
  @media only screen and (max-width: 1440px) {
    td, th {
      font-size: $font-size-xxs;
      line-height: $line-height-xxs !important;
    }
  }
}


@keyframes shine {
  0% {transition:none;}
  50% {background-color:#ccc;transition: all 0.3s ease-out;}
  100% {transition:none;}
}

// DataTable css
.data-table-wrapper {
  .data-table {
    width: 100%;
    overflow: hidden;
    background-color: white;

    thead th .relative {
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }

  thead th .relative {
    display: flex;
    justify-content: center;
    align-items: center;

    .colHeader {
      flex: 1;
    }
  }

  td span.top-row {
    font-style: italic;
  }

  /* All headers */  /* Row headers */
  .handsontable th {
    background-color: $color-white;
  }
  .ht_clone_left th {
    text-align: left;
    padding-left: $spacer * 2;
    background-color: $color-white;
  }

  /* Column headers */
  .ht_clone_top th {
    background-color: $bg-light-l2 !important;
    font-weight: $font-weight-bold;
  }

  /* Every even row, not highted one */
  .ht_master, .ht_clone_left {
    tr  {
      & > td,
      & > th:not(.ht__highlight) {
        background-color: white;
        border-bottom-color: white !important;
      }
    }
    tr:nth-of-type(even) {
      & > td,
      & > th:not(.ht__highlight) {
        background-color: $bg-light-l2;
        border-bottom-color: $bg-light-l2 !important;
      }
    }

    tr:last-child {
      & > td,
      & > th:not(.ht__highlight) {
        border-bottom-color: #DEDEDE !important;
      }
    }
  }

  .ht_clone_top_left_corner .wtHolder thead th {
    background-color: $bg-light-l2 !important;
  }

  // Ensure that content does not wrap with a new line, we want constant row height
  .htCore td {
    // white-space: nowrap;
    // text-overflow: ellipsis;
    overflow-wrap: break-word;
    word-break: break-word;
  }

  .ht_master tr:hover > td {
    background-color: #efefef;
  }

  // total avg row
  .ht_clone_bottom td {
    border-bottom: 1px solid #DEDEDE !important;
  }
  .ht_clone_bottom_left_corner tr {
    th {
      text-align: left;
      padding-left: $spacer * 2;
      font-weight: $font-weight-bold;
    }

    &:not(:last-child) {
      th {
        border-bottom: 1px solid #DEDEDE !important;
      }
    }
  }

  .metric-sheet-table {
    .ht_clone_left.handsontable {
      tr {
        background: transparent;
        td.header-row {
          // hide row text + its right border
          border-right: 0px;
          opacity: 0;
        }
      }
      .wtBorder {
        // disable highlight border when select row header
        display: none !important;
      }
    }
    td.header-row {
      // make row header to be truncate
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
}


// Pivot table css
.pivot-table-wrapper {
  .total-value, .sub-total-col-value, .h-sub-total-cell, .sub-total-field, .column-total, .column-field, .column-value, .measure-field, .row-field  {
    font-weight: $default-h-total-font-weight;
  }

  // column headers
  .column-field, .column-value {
    background: $bg-light-d1 !important;
    &:not(.remove-bottom) {
      border-bottom: 1px solid $color-dark-5 !important;
    }
    &:not(.remove-right) {
      border-right: 1px solid $color-dark-5 !important;
    }
  }

  // row headers
  .measure-field, .row-field {
    background: $bg-light-l2 !important;
  }

  .sub-total-field.row-value, .sub-total-row-value {
    background: $bg-light-l2;
    border-top: 1px solid #DEDEDE !important;
    border-bottom: 1px solid #DEDEDE !important;
  }
  .sub-total-field.column-value:not(.remove-right) {
    border-right: 1px solid $color-dark-5 !important;
  }
  .sub-total-field.row-value:not(.remove-right) {
    border-right: 1px solid $color-dark-5 !important;
  }

  // column value
  .column-value, .measure-field {
    text-align: center;
    &.total-field {
      vertical-align: center;
    }
  }

  // row-value
  .row-value, .measure-value {
    &.column-total {
      border-top: 1px solid $color-dark-5 !important;
      &.total-field:not(.remove-right) {
        border-right: 1px solid $color-dark-5 !important;
      }
    }
  }

  .ht_clone_left td.row-value[rowspan] {
    vertical-align: baseline;
    padding-top: $spacer;

    // hack to compensate for padding-top ^. https://holistics.slack.com/archives/C02DFANDBQV/p1657168789574779?thread_ts=1649896056.247839&cid=C02DFANDBQV
    height: $absolute-big-screen-cell-height - $spacer !important;
    @media only screen and (max-width: 1440px) {
      height: $absolute-small-screen-cell-height - $spacer !important;
    }
  }

  // zebra cell background for the closest category cells to value
  .ht_clone_left tr:nth-of-type(even) > td:last-child {
    background-color: $bg-light-l2;
  }

  .ht_master tr:nth-of-type(even) > td {
    background-color: $bg-light-l2;
  }

  .ht_master tr:hover > td {
    background-color: #efefef;
  }

  // all header group right border
  .ht_clone_top {
    tbody tr:first-child td:not(.remove-right) {
      border-right: 1px solid $color-dark-5 !important;
    }
  }
  .ht_clone_top_left_corner {
    td:not(.row-field):not(.remove-right), td.row-field:last-child {
      border-right: 1px solid $color-dark-5 !important;
    }
  }

  // center sortable row total column
  .ht_clone_top {
    table {
      .measure-field.sortable.custom-header {
        .cell-wrapper {
          justify-content: center;
        }
      }
    }
  }

  .highlight-bold-border-right:not(.remove-right) {
    border-right: 1px solid $color-dark-5 !important;
  }
  .highlight-bold-border-bottom:not(.remove-bottom) {
    border-bottom: 1px solid $color-dark-5 !important;
    &.row-value, &.measure-value{
      border-bottom: 1px solid #DEDEDE !important;
    }
  }
}

// Retention heatmap css
.retention-heatmap {
  .handsontable {
    // general style for all cells
    tr {
      &:not(:last-child) {
        th, td {
          border-bottom: 0;
        }
      }
    }

    .custom-header {
      font-weight: $font-weight-bold;
    }

    tr:nth-of-type(even) > td {
      background-color: $bg-light-l2;
    }

    tr > td:first-child {
      border-left: 1px solid #DEDEDE;
    }

    tr > td {
      border-right: 1px solid #DEDEDE !important;
    }

    tr > td:nth-child(2) {
      border-right: 1px solid $color-dark-5 !important;
    }

    tr > th {
      border-right: 1px solid #DEDEDE !important;
    }

    tr > th:first-child {
      border-right: 1px solid $color-dark-5;
      border-left: 1px solid #DEDEDE;
    }

    tr > th:nth-child(2) {
      border-right: 1px solid $color-dark-5 !important;
      border-left: none !important;
    }

    th:last-child {
      border-right: 1px solid #DEDEDE !important;
    }
  }
}

.metric-sheet-table {
  .ht_clone_top_left_corner {
    th:nth-child(1) {
      border-right: 0;
    }
  }

  tr:nth-of-type(even) > td {
    background-color: $bg-light-l2;
  }

  tr {
    & > td:nth-child(2),
    & > th:nth-child(2) {
      border-right: 1px solid $color-dark-5 !important;
    }
  }

  .table-header-cell {
    height: 40px;
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    justify-content: space-between;
    font-size: $font-size-xs;
    color: $color-dark-4;
  }

  .top-left-header {
    align-items: flex-start;
  }
}

// let table display full (because no need to handle pagintaion bar anymore
.widget-viz-container, .widget-content {
  .hot-table-wrapper:not(.is-pptr) {
    flex: 1;
  }
}

// only show scroll when hovering dashboard widget
.widget-viz-container, .widget-content {
  .normal-web {
    .data-table-wrapper, .metric-sheet-table {
      .ht_master {
        .wtHolder {
          &::-webkit-scrollbar {
            display: none
          }
          scrollbar-width: none;
        }
      }
      .conversion-funnel {
        &::-webkit-scrollbar {
          display: none
        }
        scrollbar-width: none;
      }
      // when hovering
      &:hover {
        .ht_master {
          .wtHolder {
            &::-webkit-scrollbar {
              display: block;
            }
            scrollbar-width: auto;
          }
        }
        .conversion-funnel {
          &::-webkit-scrollbar {
            display: block;
          }
          scrollbar-width: auto;
        }
      }
    }
    // pivot has sticky row and resize table need more cost -> just make scrollbar transparent
    // .pivot-table-wrapper:not(:hover)  {
    //   .ht_master {
    //     .wtHolder {
    //       &::-webkit-scrollbar,
    //       &::-webkit-scrollbar-track,
    //       &::-webkit-scrollbar-track-piece,
    //       &::-webkit-scrollbar-button,
    //       &::-webkit-scrollbar-thumb,
    //       &::-webkit-resizer,
    //       &::-webkit-scrollbar-corner {
    //         background: transparent !important;
    //         background-color: transparent !important;
    //       }
    //     }
    //     // firefox
    //     scrollbar-color: transparent transparent !important;
    //   }
    // }
  }
}


// For conversion funnel
.conversion-funnel-table {
  width: 100%;
  max-width: 100%;
  @apply border;

  th {
    @apply font-semibold text-gray-800;
  }

  > thead,
  > tbody {
    > tr {
      > th,
      > td {
        height: $absolute-big-screen-cell-height !important;
        line-height: $line-height-xs;
        font-size: $font-size-xs;
        padding: 0 $table-cell-padding;
        color: $color-dark-2;
        vertical-align: middle;
      }

      @media only screen and (max-width: 1440px) {
        > th,
        > td {
          height: $absolute-small-screen-cell-height !important;
          line-height: $line-height-xxs;
          font-size: $font-size-xxs;
        }
      }
    }
  }

  > thead > tr {
    background-color: $bg-light-l2;

    > th {
      border-bottom: 1px solid #DEDEDE;
      border-right: 1px solid #DEDEDE;
    }
    > th:last-child {
      border-right: 0;
    }
  }

  > tbody {
    > tr {
      cursor: pointer;

      &.selected {
        @apply font-semibold;
      }
      &:hover {
        background-color: $table-bg-hover;
      }

      > td {
        border-top: 0;
        border-right: 1px solid #DEDEDE;
      }
      > td:last-child {
        border-left: 0;
      }
    }

    > tr:nth-of-type(odd) {
      background-color: white;
    }
    > tr:nth-of-type(even) {
      @apply bg-gray-100;
    }
  }
}

<!--<docs>
  ## Usage
  <cache-status :lastCached="lastCached"
                :canCache="true"
                :loading="loading"
                :compact="true"
                :direct="true"
                :reversed="true"
                @refresh="refresh"/>

  ## Props:
  * lastCached: the absolute last cached time, which will be parsed with momentjs
  * canCache: boolean whether current user can bust cache or not
  * loading: boolean
  * compact: boolean whether to hide the short-form relative last cached time next to the icon or not
  * direct: boolean whether to trigger the refresh on the icon itself or inside the popover
  * white: boolean whether to display icon and text using white-toned color. Should set this to true when putting
  cache-status on a background-color
  * @refresh: event emitted when user click the refresh action
  * iconStatic: icon class which overrides the status icon
  * iconHover: icon class which overrides the status icon when it is hovered
  * iconRight: boolean whether the icon should be on the right if it is true
</docs>-->

<!-- Did not use uiv Popover because of conflicting stylings with other global css -->
<template>
  <HPopover
    v-if="allowBustCache"
    trigger="hover"
    :placement="!reversed ? 'bottom' : 'bottom-end'"
    class="cache-status"
    :floating-class="`${type}-cache-status cache-status__popover`"
    @mouseover="updateTime"
  >
    <CacheStatusActions
      :type="type"
      :compact="compact"
      :white="white"
      :loading="loading"
      :icon-static="iconStatic"
      :icon-hover="iconHover"
      :next-refresh="nextRefresh"
      :canceling-refresh="cancelingRefresh"
      :refresh-cancelable="refreshCancelable"
      :relative-last-cached="relativeLastCached"
      :days-diff="daysDiff"
      :can-edit-cache-settings="canEditCacheSettings"
      :icon-right="iconRight"
      :button-type="buttonType"
      @cancel-refresh="emit('cancelRefresh')"
      @refresh="refreshCache"
    />
    <template #content>
      <span v-if="!compact && direct">{{ lastCachedTitle }} {{ formattedLastCached }}</span>

      <div
        v-else
        class="big-last-cached-popover"
      >
        <div class="mb-2">
          <div
            v-if="shouldWarn"
            class="warning detail flex items-center"
          >
            <h-icon
              name="warning"
              class="mr-1 text-orange-500"
            />
            <span class="text-orange-700">
              Data might be stale.
            </span>
          </div>

          <div class="last-cache detail">
            {{ lastCachedTitle }}
            <HTooltip
              :content="formattedLastCached"
              placement="top"
            >
              <strong>{{ relativeLastCached || 'N/A' }}</strong>
            </HTooltip>
            <HPopover
              v-if="!isEmbedLink"
              placement="top"
              trigger="hover"
            >
              <h-icon
                name="question-light"
                class="ml-1 cursor-pointer text-gray-700"
              />
              <template #content>
                Don't know how cache works?
                <a
                  :href="dataCachingPublicDocsUrl"
                  target="_blank"
                >Learn more</a>
              </template>
            </HPopover>
          </div>

          <div class="pt-4">
            Click to pull the latest data
          </div>

          <div
            v-if="nextRefresh"
            class="last-cache detail"
          >
            Next refresh:
            <count-down
              class="inline font-medium"
              :end-time="nextRefresh"
              time-out-text="in a while"
              :interval="30000"
            />
          </div>
        </div>

        <template v-if="showCacheSettingsBtn && !isPublicLink">
          <hr class="m-0">

          <div>
            <HTooltip
              :content="!canEditCacheSettings ? 'Only Admin and Analyst role can edit the cache settings' : undefined"
              placement="bottom"
            >
              <HButton
                icon="configuration"
                type="tertiary-highlight"
                class="mt-2 flex"
                data-ci="ci-cache-setting-btn"
                :disabled="!canEditCacheSettings"
                block
                @click="openCacheSettings"
              >
                Cache Settings
              </HButton>
            </HTooltip>
          </div>
        </template>
      </div>
    </template>
  </HPopover>

  <div
    v-else
    class="cache-status"
  >
    <h-icon
      v-if="loading"
      name="loading"
      spin
    />
  </div>
</template>

<script setup lang="ts">
import {
  HTooltip, HPopover, HButton, type ButtonType,
} from '@holistics/design-system';
import { get } from 'lodash';
import moment, { type Moment } from 'moment-timezone';
import {
  ref, computed, watch, onMounted, onBeforeUnmount, type Ref,
} from 'vue';
import { check as checkFeatureToggle } from '@/core/services/featureToggle';
import { getMoment } from '@/core/helpers/datetime/momentHelper';
import CountDown from '@/vue_components/CountDown.vue';
import openAncientPreferencesModal, { Tabs as PreferenceTabs } from '@/modules/Preferences/services/modals/preferences.modal';
import generateDocsLink from '@/modules/FeatureToggleGroup/utils/generateDocsLink';
import { ModalResult } from '@/core/services/modal';
import * as Notifier from '@/core/services/notifier';
import { WARNING_DAYS } from '@/modules/DynamicDashboards/constants/cacheOutdated';
import { formatLastCached as defaultFormatLastCached, getRelativeLastCached as defaultGetRelativeLastCached } from './CacheStatus/helpers';
import DashboardCacheStatusActions from './CacheStatus/DashboardCacheStatusActions.vue';
import ReportCacheStatusActions from './CacheStatus/ReportCacheStatusActions.vue';

interface Props {
  source?: Record<string, unknown> | null
  id?: number
  type?: string,
  lastCached?: Date | string | number | null
  formatLastCached?: (lastCachedMoment: moment.Moment | null) => string | null
  getRelativeLastCached?: (lastCachedMoment: Moment | null, nowMoment: Moment) => string | null
  openPreferencesModal?: () => Promise<ModalResult<any>>
  showCacheSettingsBtn?: boolean
  compact?: boolean
  direct?: boolean
  loading?: boolean
  white?: boolean
  reversed?: boolean
  iconStatic?: string | null
  iconHover?: string | null
  nextRefresh?: string | Record<string, unknown> | null
  cancelingRefresh?: boolean
  refreshCancelable?: boolean
  lastCachedTitle?: string
  iconRight?: boolean
  buttonType?: ButtonType
}

const props = withDefaults(defineProps<Props>(), {
  source: null,
  id: -1,
  type: '',
  lastCached: null,
  formatLastCached: defaultFormatLastCached,
  getRelativeLastCached: defaultGetRelativeLastCached,
  showCacheSettingsBtn: false,
  compact: false,
  direct: false,
  loading: false,
  white: false,
  reverse: false,
  iconStatic: null,
  iconHover: null,
  nextRefresh: null,
  cancelingRefresh: false,
  refreshCancelable: false,
  lastCachedTitle: 'Last cached:',
  openPreferencesModal: undefined,
  iconRight: false,
  buttonType: 'secondary-default',
});

const CacheStatusActions = computed(() => (props.type === 'query_report' ? ReportCacheStatusActions : DashboardCacheStatusActions));

const emit = defineEmits<{(event: 'refresh'): void,
  (event: 'cancelRefresh'): void,
  (event: 'updateTime', payload: Moment): void,
}>();

const dataCachingPublicDocsUrl = generateDocsLink('/docs/data-caching');

const now: Ref<Moment> = ref(moment());
const updateInterval: Ref<ReturnType<typeof setInterval> | null> = ref(null);
const lastRefresh: Ref<Moment | null> = ref(null);
const schedulableTypes: Ref<Record<string, unknown>> = ref({
  query_report: {
    label: 'Report',
    model: 'QueryReport',
  },
  dashboard: {
    label: 'Dashboard',
    model: 'Dashboard',
  },
});

const lastCachedMoment = computed(() => (!props.lastCached ? null : getMoment(props.lastCached)));
const formattedLastCached = computed(() => props.formatLastCached(lastCachedMoment.value));
const relativeLastCached = computed(() => now.value && props.getRelativeLastCached(lastCachedMoment.value, now.value));
const daysDiff = computed(() => {
  if (!lastCachedMoment.value || !now.value) return 0;
  const duration = moment.duration(now.value.diff(lastCachedMoment.value));
  return duration.asDays();
});

const shouldWarn = computed(() => daysDiff.value > WARNING_DAYS);
// @ts-ignore
const canEditCacheSettings = computed(() => window.H.current_user.is_admin || window.H.current_user.is_analyst);
// @ts-ignore
const isPublicLink = computed(() => window.states.is_public_link);
// @ts-ignore
const isEmbedLink = computed(() => !!window.states.embed_link);
const allowBustCache = computed(() => (!isPublicLink.value ? true : checkFeatureToggle('embed_link:allow_public_user_bust_cache')));

function openCacheSettings () {
  if (!props.openPreferencesModal) {
    const model = get(schedulableTypes.value[props.type], 'model', 'QueryReport');
    openAncientPreferencesModal(model, props.source, PreferenceTabs.CacheSettings);
  } else {
    props.openPreferencesModal();
  }
}
function updateTime () {
  now.value = moment();
  emit('updateTime', now.value);
}
function startUpdateInterval (immediate = false) {
  if (immediate) updateTime();
  if (updateInterval.value) clearInterval(updateInterval.value);
  updateInterval.value = setInterval(updateTime, 60000);
}
function refreshCache () {
  if (!canEditCacheSettings.value) {
    // Public User can only refresh every 30s
    if (lastRefresh.value && Math.abs(lastRefresh.value.diff(moment())) < 30000) {
      Notifier.info('Please wait a moment before refreshing the data 🙇');
      return;
    }
    lastRefresh.value = moment();
  }

  if (props.loading) return;
  emit('refresh');
}

watch(() => props.lastCached, () => startUpdateInterval(true));

watch(() => props.compact, value => value === false && startUpdateInterval(true));

onMounted(() => {
  if (props.compact) return;
  startUpdateInterval();
});

onBeforeUnmount(() => updateInterval.value && clearInterval(updateInterval.value));

defineExpose({
  updateTime,
});
</script>

<style lang="postcss">
.cache-status {
  &__popover {
    z-index: 1039 !important;
  }
}

.dashboard-cache-status.cache-status__popover {
  z-index: 1039 !important;
}
</style>

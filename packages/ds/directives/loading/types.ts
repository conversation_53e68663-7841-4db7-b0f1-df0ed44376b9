import type { DirectiveBinding, ComponentPublicInstance } from 'vue';
import type { DateTime } from 'luxon';

type LoadingObject = {
  __hLoadingTicks: ReturnType<typeof setInterval> | null
}

export type BindingInstance = ComponentPublicInstance<LoadingObject>

export type MsgValue = string | boolean;

export type LoadingStepTypes = 'text' | 'skeleton' | 'progress' | 'border' | 'none';

export type TextPresets = 'random' | 'reporting' | 'data_modeling'

export type LoadingClass = LoadingStepTypes | 'wrapper' | 'container' | 'content' | 'overlay' | 'hint'

export interface Interactions {
  hoverText?: string
  onClick?: () => void
}

export type TextStep = {
  type: 'text'
  text?: string
  duration?: number
  preset?: TextPresets
} & Interactions

export interface SkeletonStep {
  type: 'skeleton'
  duration?: number
}

export interface BorderStep {
  type: 'border'
  duration?: number
}

export interface ProgressHintStep {
  compact: boolean
  text?: string
}

export type ProgressStep = {
  type: 'progress'
  text?: string
  duration?: number
  hint?: ProgressHintStep
  startTime?: DateTime,
} & Interactions

export type LoadingStep = TextStep | SkeletonStep | ProgressStep

export type LoadingSteps = LoadingStep[];

export interface BindingObjectValue {
  value: MsgValue
  steps?: LoadingSteps
  bodyClasses?: string
}

export type BindingValue = DirectiveBinding<BindingObjectValue | MsgValue>;

export interface ModifiersOptions {
  body?: boolean
  disableRelative?: boolean
  reportingPreset?: boolean
  dataModelingPreset?: boolean
}

export type InstallOptions = {
  bodyClasses?: string
}

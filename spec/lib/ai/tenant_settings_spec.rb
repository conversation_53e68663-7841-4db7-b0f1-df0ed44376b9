# typed: false
# frozen_string_literal: true

require 'rails_helper'

describe Ai::TenantSettings do
  it 'get correctly coerce to T::Struct' do
    tenant = Tenant.new(
      settings: {
        ai: {
          agents: {
            phoebe: {
              enabled: false,
            },
            meg: {
              enabled: true,
            },
          },
        },
      },
    )
    expect(tenant.settings_struct.ai.agents[Ai::Types::AgentName::Phoebe].enabled).to be(false)
    expect(tenant.settings_struct.ai.agents[Ai::Types::AgentName::Meg].enabled).to be(true)
  end

  it 'always have a default value' do
    tenant = Tenant.new(settings: {})
    expect(tenant.settings_struct.ai.agents[Ai::Types::AgentName::Phoebe].enabled).to be(true)
    expect(tenant.settings_struct.ai.agents[Ai::Types::AgentName::Meg].enabled).to be(true)
    expect(tenant.settings_struct.ai.agents[Ai::Types::AgentName::Docsearch].enabled).to be(true)
  end

  it 'does not raise error when settings have unknown agent' do
    tenant = Tenant.new(
      settings: {
        ai: {
          agents: {
            new_agent: { enabled: false },
          },
        },
      },
    )
    expect do
      expect(tenant.settings_struct.ai.agents[Ai::Types::AgentName::Phoebe].enabled).to be(true)
      expect(tenant.settings_struct.ai.agents[Ai::Types::AgentName::Meg].enabled).to be(true)
      expect(tenant.settings_struct.ai.agents[Ai::Types::AgentName::Docsearch].enabled).to be(true)
    end.not_to raise_error
  end
end
